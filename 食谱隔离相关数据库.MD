表名	字段名	数据类型	完整类型	可为空	默认值	位置	主键	外键	引用表	引用字段
administrative_areas	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
administrative_areas	name	nvarchar	nvarchar(-1)	NO	NULL	2	NO	NO	NULL	NULL
administrative_areas	code	nvarchar	nvarchar(-1)	NO	NULL	3	NO	NO	NULL	NULL
administrative_areas	level	int	int(10,0)	NO	NULL	4	NO	NO	NULL	NULL
administrative_areas	parent_id	int	int(10,0)	YES	NULL	5	NO	NO	NULL	NULL
administrative_areas	description	nvarchar	nvarchar(-1)	YES	NULL	6	NO	NO	NULL	NULL
administrative_areas	status	int	int(10,0)	NO	NULL	7	NO	NO	NULL	NULL
administrative_areas	created_at	datetime2	datetime2(1)	NO	NULL	8	NO	NO	NULL	NULL
administrative_areas	is_township_school	bit	bit	NO	NULL	9	NO	NO	NULL	NULL
ingredient_categories	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
ingredient_categories	name	nvarchar	nvarchar(-1)	NO	NULL	2	NO	NO	NULL	NULL
ingredient_categories	parent_id	int	int(10,0)	YES	NULL	3	NO	NO	NULL	NULL
ingredient_categories	description	nvarchar	nvarchar(-1)	YES	NULL	4	NO	NO	NULL	NULL
ingredient_categories	created_at	datetime2	datetime2(1)	NO	NULL	5	NO	NO	NULL	NULL
ingredient_categories	updated_at	datetime2	datetime2(1)	NO	(getdate())	6	NO	NO	NULL	NULL
ingredients	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
ingredients	name	nvarchar	nvarchar(-1)	NO	NULL	2	NO	NO	NULL	NULL
ingredients	category	nvarchar	nvarchar(-1)	NO	NULL	3	NO	NO	NULL	NULL
ingredients	unit	nvarchar	nvarchar(-1)	NO	NULL	4	NO	NO	NULL	NULL
ingredients	base_image	nvarchar	nvarchar(-1)	YES	NULL	5	NO	NO	NULL	NULL
ingredients	storage_temp	nvarchar	nvarchar(-1)	YES	NULL	6	NO	NO	NULL	NULL
ingredients	shelf_life	int	int(10,0)	YES	NULL	7	NO	NO	NULL	NULL
ingredients	created_at	datetime2	datetime2(1)	NO	NULL	8	NO	NO	NULL	NULL
ingredients	category_id	int	int(10,0)	YES	NULL	9	NO	NO	NULL	NULL
ingredients	storage_condition	nvarchar	nvarchar(-1)	YES	NULL	10	NO	NO	NULL	NULL
ingredients	specification	nvarchar	nvarchar(-1)	YES	NULL	11	NO	NO	NULL	NULL
ingredients	nutrition_info	nvarchar	nvarchar(-1)	YES	NULL	12	NO	NO	NULL	NULL
ingredients	status	int	int(10,0)	NO	NULL	13	NO	NO	NULL	NULL
ingredients	updated_at	datetime2	datetime2(1)	NO	(getdate())	14	NO	NO	NULL	NULL
ingredients	standard_unit	nvarchar	nvarchar(20)	YES	NULL	15	NO	NO	NULL	NULL
ingredients	is_condiment	bit	bit	NO	((0))	16	NO	NO	NULL	NULL
ingredients	area_id	int	int(10,0)	YES	NULL	17	NO	YES	administrative_areas	id
ingredients	is_global	bit	bit	NO	((0))	18	NO	NO	NULL	NULL
menu_plans	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
menu_plans	area_id	int	int(10,0)	NO	NULL	2	NO	NO	NULL	NULL
menu_plans	plan_date	date	date	NO	NULL	3	NO	NO	NULL	NULL
menu_plans	meal_type	nvarchar	nvarchar(-1)	NO	NULL	4	NO	NO	NULL	NULL
menu_plans	expected_diners	int	int(10,0)	YES	NULL	5	NO	NO	NULL	NULL
menu_plans	actual_diners	int	int(10,0)	YES	NULL	6	NO	NO	NULL	NULL
menu_plans	status	nvarchar	nvarchar(-1)	NO	NULL	7	NO	NO	NULL	NULL
menu_plans	created_by	int	int(10,0)	NO	NULL	8	NO	NO	NULL	NULL
menu_plans	approved_by	int	int(10,0)	YES	NULL	9	NO	NO	NULL	NULL
menu_plans	notes	nvarchar	nvarchar(-1)	YES	NULL	10	NO	NO	NULL	NULL
menu_plans	created_at	datetime2	datetime2(1)	NO	NULL	11	NO	NO	NULL	NULL
menu_plans	updated_at	datetime2	datetime2(1)	NO	(getdate())	12	NO	NO	NULL	NULL
menu_recipes	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
menu_recipes	menu_plan_id	int	int(10,0)	NO	NULL	2	NO	NO	NULL	NULL
menu_recipes	recipe_id	int	int(10,0)	NO	NULL	3	NO	NO	NULL	NULL
menu_recipes	planned_quantity	nvarchar	nvarchar(-1)	NO	NULL	4	NO	NO	NULL	NULL
menu_recipes	actual_quantity	nvarchar	nvarchar(-1)	YES	NULL	5	NO	NO	NULL	NULL
menu_recipes	notes	nvarchar	nvarchar(-1)	YES	NULL	6	NO	NO	NULL	NULL
menu_recipes	created_at	datetime2	datetime2(1)	NO	NULL	7	NO	NO	NULL	NULL
menu_recipes	updated_at	datetime2	datetime2(1)	NO	(getdate())	8	NO	NO	NULL	NULL
recipe_categories	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
recipe_categories	name	nvarchar	nvarchar(-1)	NO	NULL	2	NO	NO	NULL	NULL
recipe_categories	description	nvarchar	nvarchar(-1)	YES	NULL	3	NO	NO	NULL	NULL
recipe_categories	created_at	datetime2	datetime2(1)	YES	NULL	4	NO	NO	NULL	NULL
recipe_categories	updated_at	datetime2	datetime2(1)	YES	(getdate())	5	NO	NO	NULL	NULL
recipe_ingredients	recipe_id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
recipe_ingredients	ingredient_id	int	int(10,0)	NO	NULL	2	YES	NO	NULL	NULL
recipe_ingredients	quantity	nvarchar	nvarchar(-1)	NO	NULL	3	NO	NO	NULL	NULL
recipe_ingredients	unit	nvarchar	nvarchar(-1)	NO	NULL	4	NO	NO	NULL	NULL
recipes	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
recipes	name	nvarchar	nvarchar(-1)	NO	NULL	2	NO	NO	NULL	NULL
recipes	category	nvarchar	nvarchar(-1)	NO	NULL	3	NO	NO	NULL	NULL
recipes	main_image	nvarchar	nvarchar(-1)	YES	NULL	4	NO	NO	NULL	NULL
recipes	calories	int	int(10,0)	YES	NULL	5	NO	NO	NULL	NULL
recipes	cooking_steps	nvarchar	nvarchar(-1)	YES	NULL	6	NO	NO	NULL	NULL
recipes	created_by	int	int(10,0)	NO	NULL	7	NO	NO	NULL	NULL
recipes	created_at	datetime2	datetime2(1)	YES	NULL	8	NO	NO	NULL	NULL
recipes	category_id	int	int(10,0)	YES	NULL	9	NO	NO	NULL	NULL
recipes	meal_type	nvarchar	nvarchar(-1)	YES	NULL	10	NO	NO	NULL	NULL
recipes	description	nvarchar	nvarchar(-1)	YES	NULL	11	NO	NO	NULL	NULL
recipes	nutrition_info	nvarchar	nvarchar(-1)	YES	NULL	12	NO	NO	NULL	NULL
recipes	cooking_method	nvarchar	nvarchar(-1)	YES	NULL	13	NO	NO	NULL	NULL
recipes	cooking_time	int	int(10,0)	YES	NULL	14	NO	NO	NULL	NULL
recipes	serving_size	int	int(10,0)	YES	NULL	15	NO	NO	NULL	NULL
recipes	status	int	int(10,0)	NO	NULL	16	NO	NO	NULL	NULL
recipes	updated_at	datetime2	datetime2(1)	YES	(getdate())	17	NO	NO	NULL	NULL
recipes	parent_id	int	int(10,0)	YES	NULL	18	NO	NO	NULL	NULL
recipes	is_template	bit	bit	YES	NULL	19	NO	NO	NULL	NULL
recipes	template_type	nvarchar	nvarchar(-1)	YES	NULL	20	NO	NO	NULL	NULL
recipes	variation_reason	nvarchar	nvarchar(-1)	YES	NULL	21	NO	NO	NULL	NULL
recipes	version	int	int(10,0)	YES	NULL	22	NO	NO	NULL	NULL
recipes	is_user_defined	bit	bit	NO	((0))	23	NO	NO	NULL	NULL
recipes	priority	int	int(10,0)	NO	((0))	24	NO	NO	NULL	NULL
recipes	area_id	int	int(10,0)	YES	NULL	25	NO	YES	administrative_areas	id
recipes	is_global	bit	bit	NO	((0))	26	NO	NO	NULL	NULL
users	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
users	username	nvarchar	nvarchar(-1)	NO	NULL	2	NO	NO	NULL	NULL
users	password_hash	nvarchar	nvarchar(-1)	NO	NULL	3	NO	NO	NULL	NULL
users	email	nvarchar	nvarchar(-1)	YES	NULL	4	NO	NO	NULL	NULL
users	real_name	nvarchar	nvarchar(-1)	YES	NULL	5	NO	NO	NULL	NULL
users	phone	nvarchar	nvarchar(-1)	YES	NULL	6	NO	NO	NULL	NULL
users	avatar	nvarchar	nvarchar(-1)	YES	NULL	7	NO	NO	NULL	NULL
users	last_login	datetime2	datetime2(1)	YES	NULL	8	NO	NO	NULL	NULL
users	status	int	int(10,0)	NO	NULL	9	NO	NO	NULL	NULL
users	area_id	int	int(10,0)	YES	NULL	10	NO	NO	NULL	NULL
users	area_level	int	int(10,0)	YES	NULL	11	NO	NO	NULL	NULL
users	created_at	datetime2	datetime2(1)	NO	NULL	12	NO	NO	NULL	NULL
weekly_menu_recipes	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
weekly_menu_recipes	weekly_menu_id	int	int(10,0)	NO	NULL	2	NO	YES	weekly_menus	id
weekly_menu_recipes	day_of_week	int	int(10,0)	NO	NULL	3	NO	NO	NULL	NULL
weekly_menu_recipes	meal_type	nvarchar	nvarchar(20)	NO	NULL	4	NO	NO	NULL	NULL
weekly_menu_recipes	recipe_id	int	int(10,0)	YES	NULL	5	NO	YES	recipes	id
weekly_menu_recipes	recipe_name	nvarchar	nvarchar(100)	NO	NULL	6	NO	NO	NULL	NULL
weekly_menu_recipes	created_at	datetime2	datetime2(1)	NO	(getdate())	7	NO	NO	NULL	NULL
weekly_menu_recipes	updated_at	datetime2	datetime2(1)	NO	(getdate())	8	NO	NO	NULL	NULL
weekly_menus	id	int	int(10,0)	NO	NULL	1	YES	NO	NULL	NULL
weekly_menus	area_id	int	int(10,0)	NO	NULL	2	NO	YES	administrative_areas	id
weekly_menus	week_start	date	date	NO	NULL	3	NO	NO	NULL	NULL
weekly_menus	week_end	date	date	NO	NULL	4	NO	NO	NULL	NULL
weekly_menus	status	nvarchar	nvarchar(20)	NO	('计划中')	5	NO	NO	NULL	NULL
weekly_menus	created_by	int	int(10,0)	NO	NULL	6	NO	YES	users	id
weekly_menus	created_at	datetime2	datetime2(1)	NO	(getdate())	7	NO	NO	NULL	NULL
weekly_menus	updated_at	datetime2	datetime2(1)	NO	(getdate())	8	NO	NO	NULL	NULL
