#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食材批量导入超级工具
从D:\FOOT文件夹批量导入食材数据到系统中

功能特点:
1. 🔍 智能扫描文件夹结构
2. 🖼️ 自动图片压缩处理 (400x400)
3. 🔄 去重检查避免重复导入
4. 📊 实时进度显示
5. 📝 详细导入报告
6. 🏫 支持学校级数据隔离
"""

import os
import sys
import json
import shutil
import uuid
import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict
from typing import List, Dict, Optional, Tuple

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from PIL import Image, ImageOps
except ImportError:
    print("❌ 需要安装Pillow库: pip install Pillow")
    sys.exit(1)

# Flask应用相关导入
try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory, AdministrativeArea
    from flask_login import current_user
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入Flask应用模块失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

class IngredientBatchImporter:
    """食材批量导入器"""

    def __init__(self, source_folder="D:\\FOOT", target_folder=None):
        """
        初始化导入器

        Args:
            source_folder: 源文件夹路径
            target_folder: 目标文件夹路径（项目uploads目录）
        """
        self.source_folder = Path(source_folder)
        self.target_folder = target_folder or Path(project_root) / "app" / "static" / "uploads" / "ingredients"

        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}

        # 图片压缩设置
        self.target_size = (400, 400)
        self.quality = 85

        # 统计信息
        self.stats = {
            'total_files': 0,
            'image_files': 0,
            'processed_images': 0,
            'imported_ingredients': 0,
            'skipped_duplicates': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }

        # 错误日志
        self.errors = []

        # 食材分类映射（可根据文件夹结构自动推断）
        self.category_mapping = {
            '蔬菜': ['蔬菜', 'vegetables', '青菜', '菜类'],
            '肉类': ['肉类', 'meat', '肉', '荤菜'],
            '水果': ['水果', 'fruit', '果类'],
            '调料': ['调料', 'seasoning', '调味品', '香料'],
            '主食': ['主食', 'staple', '米面', '粮食'],
            '豆类': ['豆类', 'beans', '豆制品'],
            '海鲜': ['海鲜', 'seafood', '水产'],
            '其他': ['其他', 'other', 'misc']
        }

        print(f"🚀 食材批量导入器初始化完成")
        print(f"📂 源文件夹: {self.source_folder}")
        print(f"📁 目标文件夹: {self.target_folder}")

    def check_prerequisites(self) -> bool:
        """检查导入前提条件"""
        print("\n🔍 检查导入前提条件...")

        # 检查源文件夹
        if not self.source_folder.exists():
            print(f"❌ 源文件夹不存在: {self.source_folder}")
            return False

        # 检查目标文件夹，不存在则创建
        try:
            self.target_folder.mkdir(parents=True, exist_ok=True)
            print(f"✅ 目标文件夹准备就绪: {self.target_folder}")
        except Exception as e:
            print(f"❌ 无法创建目标文件夹: {e}")
            return False

        # 检查数据库连接
        try:
            with db.engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) FROM ingredients"))
                existing_count = result.scalar()
                print(f"✅ 数据库连接正常，现有食材数量: {existing_count}")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

        return True

    def scan_source_folder(self) -> Dict:
        """扫描源文件夹，分析文件结构"""
        print(f"\n📂 扫描源文件夹: {self.source_folder}")

        scan_result = {
            'total_files': 0,
            'image_files': [],
            'categories': defaultdict(list),
            'potential_ingredients': []
        }

        for root, dirs, files in os.walk(self.source_folder):
            for file in files:
                file_path = Path(root) / file
                scan_result['total_files'] += 1

                # 检查是否为图片文件
                if file_path.suffix.lower() in self.image_extensions:
                    # 推断分类（基于文件夹名称）
                    category = self._infer_category_from_path(file_path)

                    # 推断食材名称（基于文件名）
                    ingredient_name = self._infer_ingredient_name(file_path.stem)

                    image_info = {
                        'file_path': file_path,
                        'category': category,
                        'ingredient_name': ingredient_name,
                        'size': file_path.stat().st_size
                    }

                    scan_result['image_files'].append(image_info)
                    scan_result['categories'][category].append(image_info)
                    scan_result['potential_ingredients'].append(ingredient_name)

        self.stats['total_files'] = scan_result['total_files']
        self.stats['image_files'] = len(scan_result['image_files'])

        print(f"📊 扫描完成:")
        print(f"  📄 总文件数: {scan_result['total_files']}")
        print(f"  🖼️ 图片文件: {len(scan_result['image_files'])}")
        print(f"  📂 发现分类: {len(scan_result['categories'])}")

        return scan_result

    def _infer_category_from_path(self, file_path: Path) -> str:
        """从文件路径推断食材分类"""
        path_parts = [part.lower() for part in file_path.parts]

        for category, keywords in self.category_mapping.items():
            for keyword in keywords:
                if any(keyword in part for part in path_parts):
                    return category

        return '其他'

    def _infer_ingredient_name(self, filename: str) -> str:
        """从文件名推断食材名称"""
        # 移除常见的文件名后缀和编号
        name = re.sub(r'[_\-\d]+$', '', filename)
        name = re.sub(r'[_\-]', ' ', name)
        name = name.strip()

        # 如果名称为空或太短，使用原始文件名
        if len(name) < 2:
            name = filename

        return name

    def compress_image(self, source_path: Path, target_path: Path) -> bool:
        """
        压缩图片到指定尺寸

        Args:
            source_path: 源图片路径
            target_path: 目标图片路径

        Returns:
            bool: 是否成功
        """
        try:
            with Image.open(source_path) as img:
                # 转换为RGB模式（处理RGBA、P等模式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # 使用高质量重采样算法调整大小
                img = ImageOps.fit(img, self.target_size, Image.Resampling.LANCZOS)

                # 保存压缩后的图片
                img.save(target_path, 'JPEG', quality=self.quality, optimize=True)

                return True

        except Exception as e:
            self.errors.append(f"图片压缩失败 {source_path}: {e}")
            return False

    def check_duplicate_ingredient(self, name: str, area_id: int = None) -> Optional[Ingredient]:
        """
        检查是否存在重复的食材

        Args:
            name: 食材名称
            area_id: 学校区域ID

        Returns:
            Ingredient: 如果存在重复则返回食材对象，否则返回None
        """
        query = Ingredient.query.filter(Ingredient.name == name)

        if area_id:
            # 检查同一学校的食材
            query = query.filter(
                db.or_(
                    Ingredient.area_id == area_id,
                    Ingredient.is_global == True
                )
            )

        return query.first()

    def get_or_create_category(self, category_name: str) -> IngredientCategory:
        """获取或创建食材分类"""
        category = IngredientCategory.query.filter_by(name=category_name).first()

        if not category:
            category = IngredientCategory(
                name=category_name,
                description=f"自动创建的{category_name}分类"
            )
            db.session.add(category)
            db.session.flush()  # 获取ID但不提交

        return category

    def process_single_ingredient(self, image_info: Dict, area_id: int = None) -> bool:
        """
        处理单个食材的导入

        Args:
            image_info: 图片信息字典
            area_id: 学校区域ID

        Returns:
            bool: 是否成功导入
        """
        try:
            ingredient_name = image_info['ingredient_name']
            category_name = image_info['category']
            source_path = image_info['file_path']

            # 检查重复
            existing = self.check_duplicate_ingredient(ingredient_name, area_id)
            if existing:
                print(f"⏭️ 跳过重复食材: {ingredient_name}")
                self.stats['skipped_duplicates'] += 1
                return True

            # 处理图片
            image_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}.jpg"
            target_image_path = self.target_folder / image_filename

            if not self.compress_image(source_path, target_image_path):
                return False

            # 获取或创建分类
            category = self.get_or_create_category(category_name)

            # 使用原始SQL创建食材记录（避免DATETIME2精度问题）
            sql = text("""
                INSERT INTO ingredients
                (name, category, category_id, area_id, unit, base_image, is_global, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :category_id, :area_id, :unit, :base_image, :is_global, :status, GETDATE(), GETDATE())
            """)

            params = {
                'name': ingredient_name,
                'category': category_name,
                'category_id': category.id,
                'area_id': area_id,
                'unit': '份',
                'base_image': f"/static/uploads/ingredients/{image_filename}",
                'is_global': False if area_id else True,
                'status': 1
            }

            result = db.session.execute(sql, params)
            ingredient_id = result.fetchone()[0]

            self.stats['imported_ingredients'] += 1
            self.stats['processed_images'] += 1

            print(f"✅ 导入食材: {ingredient_name} ({category_name}) [ID: {ingredient_id}]")
            return True

        except Exception as e:
            error_msg = f"处理食材失败 {image_info.get('ingredient_name', 'Unknown')}: {e}"
            self.errors.append(error_msg)
            self.stats['errors'] += 1
            print(f"❌ {error_msg}")
            return False

    def batch_import(self, scan_result: Dict, area_id: int = None, batch_size: int = 50) -> Dict:
        """
        批量导入食材

        Args:
            scan_result: 扫描结果
            area_id: 学校区域ID
            batch_size: 批次大小

        Returns:
            Dict: 导入结果统计
        """
        print(f"\n🚀 开始批量导入食材...")
        print(f"📊 待处理图片: {len(scan_result['image_files'])}")

        self.stats['start_time'] = datetime.now()

        image_files = scan_result['image_files']
        total_files = len(image_files)

        # 按批次处理
        for i in range(0, total_files, batch_size):
            batch = image_files[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (total_files + batch_size - 1) // batch_size

            print(f"\n📦 处理批次 {batch_num}/{total_batches} ({len(batch)} 个文件)")

            try:
                # 处理当前批次
                for j, image_info in enumerate(batch):
                    progress = ((i + j + 1) / total_files) * 100
                    print(f"[{progress:.1f}%] ", end="")

                    self.process_single_ingredient(image_info, area_id)

                # 提交当前批次
                db.session.commit()
                print(f"✅ 批次 {batch_num} 提交成功")

            except Exception as e:
                db.session.rollback()
                error_msg = f"批次 {batch_num} 处理失败: {e}"
                self.errors.append(error_msg)
                print(f"❌ {error_msg}")
                continue

        self.stats['end_time'] = datetime.now()

        return self.generate_import_report()

    def generate_import_report(self) -> Dict:
        """生成导入报告"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']

        report = {
            'summary': {
                'total_files_scanned': self.stats['total_files'],
                'image_files_found': self.stats['image_files'],
                'images_processed': self.stats['processed_images'],
                'ingredients_imported': self.stats['imported_ingredients'],
                'duplicates_skipped': self.stats['skipped_duplicates'],
                'errors_occurred': self.stats['errors'],
                'duration_seconds': duration.total_seconds() if duration else None,
                'success_rate': (self.stats['imported_ingredients'] / max(self.stats['image_files'], 1)) * 100
            },
            'errors': self.errors,
            'timestamp': datetime.now().isoformat()
        }

        return report

    def print_import_report(self, report: Dict):
        """打印导入报告"""
        print("\n" + "="*60)
        print("📊 **食材批量导入报告**")
        print("="*60)

        summary = report['summary']

        print(f"📂 扫描文件总数: {summary['total_files_scanned']}")
        print(f"🖼️ 发现图片文件: {summary['image_files_found']}")
        print(f"✅ 成功导入食材: {summary['ingredients_imported']}")
        print(f"⏭️ 跳过重复食材: {summary['duplicates_skipped']}")
        print(f"❌ 处理错误数量: {summary['errors_occurred']}")
        print(f"📈 成功率: {summary['success_rate']:.1f}%")

        if summary['duration_seconds']:
            print(f"⏱️ 总耗时: {summary['duration_seconds']:.2f} 秒")

        if report['errors']:
            print(f"\n❌ **错误详情** ({len(report['errors'])} 个):")
            for i, error in enumerate(report['errors'][:10], 1):
                print(f"  {i}. {error}")
            if len(report['errors']) > 10:
                print(f"  ... 还有 {len(report['errors']) - 10} 个错误")

        print("\n✅ 导入完成！")

    def save_report_to_file(self, report: Dict, filename: str = None):
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ingredient_import_report_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"💾 导入报告已保存到: {filename}")

def main():
    """主函数"""
    print("🍎 **食材批量导入超级工具**")
    print("="*60)

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 创建导入器
        importer = IngredientBatchImporter()

        # 检查前提条件
        if not importer.check_prerequisites():
            print("❌ 前提条件检查失败，退出程序")
            return

        # 扫描源文件夹
        scan_result = importer.scan_source_folder()

        if not scan_result['image_files']:
            print("❌ 未发现任何图片文件，退出程序")
            return

        # 询问用户确认
        print(f"\n🤔 发现 {len(scan_result['image_files'])} 个图片文件")
        print("分类分布:")
        for category, items in scan_result['categories'].items():
            print(f"  📂 {category}: {len(items)} 个")

        confirm = input("\n是否继续导入？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消导入")
            return

        # 获取学校区域ID（可选）
        area_id = None
        use_area = input("是否指定学校区域？(y/N): ").strip().lower()
        if use_area == 'y':
            try:
                area_id = int(input("请输入学校区域ID: ").strip())
                area = AdministrativeArea.query.get(area_id)
                if not area:
                    print(f"❌ 未找到ID为 {area_id} 的学校区域")
                    return
                print(f"✅ 将导入到学校: {area.name}")
            except ValueError:
                print("❌ 无效的区域ID")
                return

        # 开始批量导入
        report = importer.batch_import(scan_result, area_id)

        # 显示和保存报告
        importer.print_import_report(report)
        importer.save_report_to_file(report)

if __name__ == "__main__":
    main()
