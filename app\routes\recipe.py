from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Recipe, RecipeCategory, RecipeIngredient, RecipeProcess, RecipeProcessIngredient
from app.utils.log_activity import log_activity
from werkzeug.utils import secure_filename
import os
from datetime import datetime
from sqlalchemy import text

recipe_bp = Blueprint('recipe', __name__)

@recipe_bp.route('/')
@login_required
def index():
    """食谱列表页面 - 实现学校级数据隔离"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食谱和全局食谱，排除软删除的食谱
    query = Recipe.query.filter(
        Recipe.is_deleted == False,  # 排除软删除的食谱
        db.or_(
            Recipe.area_id.in_(area_ids),  # 用户学校的食谱
            Recipe.is_global == True,      # 全局食谱（系统预设）
            Recipe.area_id.is_(None)       # 兼容旧数据（无area_id的食谱）
        )
    )

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Recipe.name.like(f'%{keyword}%'))

    pagination = query.order_by(Recipe.id.desc()).paginate(page=page, per_page=per_page)
    recipes = pagination.items

    categories = RecipeCategory.query.all()

    return render_template('recipe/index.html',
                          recipes=recipes,
                          pagination=pagination,
                          categories=categories,
                          category_id=category_id,
                          keyword=keyword)

@recipe_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建食谱"""
    from app.forms.recipe import RecipeForm

    form = RecipeForm()

    # 动态加载分类选项
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [
        (c.id, c.name) for c in RecipeCategory.query.all()
    ]

    if form.validate_on_submit():
        # 处理图片上传
        main_image = None
        if form.main_image.data:
            file = form.main_image.data
            filename = secure_filename(file.filename)
            # 确保文件名唯一
            unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
            upload_folder = os.path.join(current_app.static_folder, 'uploads/recipes')

            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=True)

            file_path = os.path.join(upload_folder, unique_filename)
            file.save(file_path)

            # 存储相对路径
            main_image = f"uploads/recipes/{unique_filename}"

        # 使用原生SQL创建食谱记录

        # 准备参数
        category_id = form.category_id.data if form.category_id.data and form.category_id.data != 0 else None
        priority = 10 if form.is_user_defined.data else 0  # 用户自定义食谱优先级设为10

        # 获取分类名称
        category_name = None
        if category_id:
            category = RecipeCategory.query.get(category_id)
            if category:
                category_name = category.name

        # 获取当前用户的学校区域
        user_area = current_user.get_current_area()
        if not user_area:
            flash('无法确定您的学校信息，请联系管理员', 'error')
            return redirect(url_for('recipe.index'))

        # 使用原生SQL插入记录，不包含created_at和updated_at字段
        sql = text("""
            INSERT INTO recipes
            (name, category, category_id, area_id, meal_type, main_image, description, status, is_user_defined, is_global, priority, created_by)
            OUTPUT inserted.id
            VALUES
            (:name, :category, :category_id, :area_id, :meal_type, :main_image, :description, :status, :is_user_defined, :is_global, :priority, :created_by)
        """)

        params = {
            'name': form.name.data,
            'category': category_name,
            'category_id': category_id,
            'area_id': user_area.id,  # 绑定到用户的学校
            'meal_type': form.meal_type.data,
            'main_image': main_image,
            'description': form.description.data,
            'status': form.status.data,
            'is_user_defined': 1 if form.is_user_defined.data else 0,  # 转换为整数
            'is_global': 0,  # 用户创建的食谱不是全局食谱
            'priority': priority,
            'created_by': current_user.id
        }

        try:
            # 执行SQL并获取新插入记录的ID
            result = db.session.execute(sql, params)
            recipe_id = result.fetchone()[0]

            # 创建一个Recipe对象用于后续操作
            recipe = Recipe.query.get(recipe_id)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建食谱失败: {str(e)}")
            flash(f'创建食谱失败: {str(e)}', 'danger')
            return render_template('recipe/create.html', form=form, recipe=None)

        # 处理食材关联
        ingredient_ids = request.form.getlist('ingredient_ids[]')
        ingredient_quantities = request.form.getlist('ingredient_quantities[]')
        ingredient_units = request.form.getlist('ingredient_units[]')

        if ingredient_ids:
            for i in range(len(ingredient_ids)):
                ingredient_id = ingredient_ids[i]
                quantity = ingredient_quantities[i] if i < len(ingredient_quantities) else 1
                unit = ingredient_units[i] if i < len(ingredient_units) else '份'

                recipe_ingredient = RecipeIngredient(
                    recipe_id=recipe.id,
                    ingredient_id=ingredient_id,
                    quantity=quantity,
                    unit=unit
                )
                db.session.add(recipe_ingredient)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='Recipe',
            resource_id=recipe.id,
            details=recipe.to_dict()
        )

        db.session.commit()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': '食谱创建成功！',
                'id': recipe.id
            })
        else:
            flash('食谱创建成功！', 'success')
            return redirect(url_for('recipe.view', id=recipe.id))

    # GET 请求，显示创建表单
    return render_template('recipe/create.html', form=form, recipe=None)

@recipe_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑食谱"""
    from app.forms.recipe import RecipeForm

    recipe = Recipe.query.get_or_404(id)
    recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()

    form = RecipeForm(obj=recipe)

    # 动态加载分类选项
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [
        (c.id, c.name) for c in RecipeCategory.query.all()
    ]

    if form.validate_on_submit():
        try:
            old_data = recipe.to_dict()

            # 处理图片上传
            if form.main_image.data:
                file = form.main_image.data
                filename = secure_filename(file.filename)
                # 确保文件名唯一
                unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
                upload_folder = os.path.join(current_app.static_folder, 'uploads/recipes')

                # 确保上传目录存在
                os.makedirs(upload_folder, exist_ok=True)

                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)

                # 删除旧图片
                if recipe.main_image:
                    old_file_path = os.path.join(current_app.static_folder, recipe.main_image)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)

                # 存储相对路径
                recipe.main_image = f"uploads/recipes/{unique_filename}"

            # 使用原生SQL更新食谱信息

            # 准备参数
            category_id = form.category_id.data if form.category_id.data and form.category_id.data != 0 else None
            priority = 10 if form.is_user_defined.data else 0
            is_user_defined = 1 if form.is_user_defined.data else 0

            # 获取分类名称
            category_name = None
            if category_id:
                category = RecipeCategory.query.get(category_id)
                if category:
                    category_name = category.name

            # 使用原生SQL更新记录，不包含updated_at字段
            sql = text("""
                UPDATE recipes
                SET name = :name,
                    category = :category,
                    category_id = :category_id,
                    meal_type = :meal_type,
                    description = :description,
                    status = :status,
                    is_user_defined = :is_user_defined,
                    priority = :priority,
                    main_image = :main_image
                WHERE id = :id
            """)

            params = {
                'name': form.name.data,
                'category': category_name,
                'category_id': category_id,
                'meal_type': form.meal_type.data,
                'description': form.description.data,
                'status': form.status.data,
                'is_user_defined': is_user_defined,
                'priority': priority,
                'main_image': recipe.main_image,  # 使用已更新的main_image
                'id': recipe.id
            }

            try:
                # 执行SQL
                db.session.execute(sql, params)

                # 重新加载recipe对象以获取最新数据
                db.session.refresh(recipe)
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"更新食谱失败: {str(e)}")
                flash(f'更新食谱失败: {str(e)}', 'danger')
                return render_template('recipe/create.html', form=form, recipe=recipe, recipe_ingredients=recipe_ingredients)

            # 处理食材关联
            # 先删除现有的食材关联
            RecipeIngredient.query.filter_by(recipe_id=recipe.id).delete()

            # 添加新的食材关联
            ingredient_ids = request.form.getlist('ingredient_ids[]')
            ingredient_quantities = request.form.getlist('ingredient_quantities[]')
            ingredient_units = request.form.getlist('ingredient_units[]')

            if ingredient_ids:
                for i in range(len(ingredient_ids)):
                    ingredient_id = ingredient_ids[i]
                    quantity = ingredient_quantities[i] if i < len(ingredient_quantities) else 1
                    unit = ingredient_units[i] if i < len(ingredient_units) else '份'

                    recipe_ingredient = RecipeIngredient(
                        recipe_id=recipe.id,
                        ingredient_id=ingredient_id,
                        quantity=quantity,
                        unit=unit
                    )
                    db.session.add(recipe_ingredient)

            # 添加审计日志
            log_activity(
                action='update',
                resource_type='Recipe',
                resource_id=recipe.id,
                details={
                    'old': old_data,
                    'new': recipe.to_dict()
                }
            )

            db.session.commit()

            # 根据请求类型返回不同的响应
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'message': '食谱更新成功！',
                    'id': recipe.id
                })
            else:
                flash('食谱更新成功！', 'success')
                return redirect(url_for('recipe.view', id=recipe.id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新食谱异常: {str(e)}")

            # 根据请求类型返回不同的响应
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'操作异常: {str(e)}'}), 500
            else:
                flash(f'操作异常: {str(e)}', 'danger')
                return redirect(url_for('recipe.edit', id=recipe.id))

    # GET 请求，显示编辑表单
    return render_template('recipe/create.html',
                          form=form,
                          recipe=recipe,
                          recipe_ingredients=recipe_ingredients)

@recipe_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """软删除食谱 - 保护留样和溯源数据完整性"""
    try:
        recipe = Recipe.query.get_or_404(id)

        # 权限检查：只能删除自己学校的食谱，不能删除系统食谱
        user_area = current_user.get_current_area()
        if recipe.is_global or (recipe.area_id and recipe.area_id != user_area.id):
            return jsonify({
                'success': False,
                'message': '您没有权限删除此食谱'
            })

        # 检查是否已经被软删除
        if recipe.is_soft_deleted:
            return jsonify({
                'success': False,
                'message': '该食谱已被删除'
            })

        current_app.logger.info(f'软删除食谱 - ID: {id}, 名称: {recipe.name}')

        # 添加审计日志
        log_activity(
            action='soft_delete',
            resource_type='Recipe',
            resource_id=recipe.id,
            details=recipe.to_dict()
        )

        # 执行软删除 - 使用原始SQL避免DATETIME2精度问题
        from sqlalchemy import text

        soft_delete_sql = text("""
            UPDATE recipes
            SET is_deleted = 1,
                deleted_at = GETDATE(),
                deleted_by = :deleted_by
            WHERE id = :recipe_id
        """)

        db.session.execute(soft_delete_sql, {
            'deleted_by': current_user.id,
            'recipe_id': recipe.id
        })

        db.session.commit()

        current_app.logger.info(f'食谱软删除成功 - ID: {id}')

        return jsonify({
            'success': True,
            'message': '食谱已删除！数据已保留以确保留样和溯源功能正常。',
            'details': {
                'deleted_by': current_user.real_name or current_user.username
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"软删除食谱异常: {str(e)}")
        return jsonify({'success': False, 'message': f'操作异常: {str(e)}'})


@recipe_bp.route('/<int:id>/restore', methods=['POST'])
@login_required
def restore(id):
    """恢复软删除的食谱"""
    try:
        recipe = Recipe.query.get_or_404(id)

        # 权限检查：只能恢复自己学校的食谱
        user_area = current_user.get_current_area()
        if recipe.area_id and recipe.area_id != user_area.id:
            return jsonify({
                'success': False,
                'message': '您没有权限恢复此食谱'
            })

        # 检查是否已经被软删除
        if not recipe.is_soft_deleted:
            return jsonify({
                'success': False,
                'message': '该食谱未被删除，无需恢复'
            })

        current_app.logger.info(f'恢复食谱 - ID: {id}, 名称: {recipe.name}')

        # 添加审计日志
        log_activity(
            action='restore',
            resource_type='Recipe',
            resource_id=recipe.id,
            details=recipe.to_dict()
        )

        # 执行恢复 - 使用原始SQL避免DATETIME2精度问题
        from sqlalchemy import text

        restore_sql = text("""
            UPDATE recipes
            SET is_deleted = 0,
                deleted_at = NULL,
                deleted_by = NULL
            WHERE id = :recipe_id
        """)

        db.session.execute(restore_sql, {
            'recipe_id': recipe.id
        })

        db.session.commit()

        current_app.logger.info(f'食谱恢复成功 - ID: {id}')

        return jsonify({
            'success': True,
            'message': '食谱已恢复！',
            'details': {
                'restored_by': current_user.real_name or current_user.username
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"恢复食谱异常: {str(e)}")
        return jsonify({'success': False, 'message': f'操作异常: {str(e)}'})


def restore_and_update_recipe_copy(existing_copy, original_recipe, current_user):
    """恢复软删除的食谱副本并更新为最新内容 - 使用原始SQL避免DATETIME2精度问题"""
    try:
        current_app.logger.info(f'开始恢复并更新食谱副本 - ID: {existing_copy.id}')

        from sqlalchemy import text

        # 1. 使用原始SQL恢复软删除状态并更新内容，不包含updated_at字段
        new_description = f"基于系统食谱《{original_recipe.name}》创建的学校专用版本。原描述：{original_recipe.description or '无'}（已更新）"

        update_sql = text("""
            UPDATE recipes
            SET category = :category,
                category_id = :category_id,
                meal_type = :meal_type,
                main_image = :main_image,
                description = :description,
                priority = :priority,
                is_deleted = 0,
                deleted_at = NULL,
                deleted_by = NULL
            WHERE id = :recipe_id
        """)

        db.session.execute(update_sql, {
            'category': original_recipe.category or '',
            'category_id': original_recipe.category_id,
            'meal_type': original_recipe.meal_type or '',
            'main_image': original_recipe.main_image or '',
            'description': new_description,
            'priority': getattr(original_recipe, 'priority', 0) or 0,
            'recipe_id': existing_copy.id
        })

        current_app.logger.info(f'食谱基本信息更新完成 - ID: {existing_copy.id}')

        # 2. 删除旧的食材关系
        db.session.execute(
            text("DELETE FROM recipe_ingredients WHERE recipe_id = :recipe_id"),
            {'recipe_id': existing_copy.id}
        )

        current_app.logger.info(f'旧食材关系删除完成 - ID: {existing_copy.id}')

        # 3. 复制最新的食材关系
        from app.models import RecipeIngredient
        original_ingredients = RecipeIngredient.query.filter_by(recipe_id=original_recipe.id).all()

        for orig_ingredient in original_ingredients:
            ingredient_sql = text("""
                INSERT INTO recipe_ingredients
                (recipe_id, ingredient_id, quantity, unit)
                VALUES
                (:recipe_id, :ingredient_id, :quantity, :unit)
            """)

            db.session.execute(ingredient_sql, {
                'recipe_id': existing_copy.id,
                'ingredient_id': orig_ingredient.ingredient_id,
                'quantity': float(orig_ingredient.quantity) if orig_ingredient.quantity else 0.0,
                'unit': orig_ingredient.unit or ''
            })

        current_app.logger.info(f'新食材关系复制完成 - ID: {existing_copy.id}, 食材数量: {len(original_ingredients)}')

        # 4. 提交更改
        db.session.commit()

        current_app.logger.info(f'食谱副本恢复并更新成功 - ID: {existing_copy.id}')

        return jsonify({
            'success': True,
            'message': f'已恢复并更新食谱《{existing_copy.name}》为最新版本，您可以继续进行个性化修改',
            'recipe_id': existing_copy.id,
            'action': 'restored_and_updated'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'恢复并更新食谱副本失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'恢复失败: {str(e)}'
        })


def check_recipe_dependencies(recipe_id):
    """检查食谱的依赖关系，确保删除不会影响留样和溯源"""
    dependencies = {
        'food_samples': 0,
        'menu_recipes': 0,
        'weekly_menu_recipes': 0,
        'consumption_plans': 0,
        'can_delete': True,
        'message': ''
    }

    try:
        # 检查留样记录
        food_samples_count = db.session.execute(
            text("SELECT COUNT(*) FROM food_samples WHERE recipe_id = :recipe_id"),
            {'recipe_id': recipe_id}
        ).scalar() or 0

        # 检查菜单关联
        menu_recipes_count = db.session.execute(
            text("SELECT COUNT(*) FROM menu_recipes WHERE recipe_id = :recipe_id"),
            {'recipe_id': recipe_id}
        ).scalar() or 0

        # 检查周菜单关联
        weekly_menu_recipes_count = db.session.execute(
            text("SELECT COUNT(*) FROM weekly_menu_recipes WHERE recipe_id = :recipe_id"),
            {'recipe_id': recipe_id}
        ).scalar() or 0

        # 检查消耗计划关联（通过菜单计划间接关联）
        consumption_plans_count = db.session.execute(
            text("""
                SELECT COUNT(DISTINCT cp.id)
                FROM consumption_plans cp
                JOIN menu_plans mp ON cp.menu_plan_id = mp.id
                JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
                WHERE mr.recipe_id = :recipe_id
            """),
            {'recipe_id': recipe_id}
        ).scalar() or 0

        dependencies['food_samples'] = food_samples_count
        dependencies['menu_recipes'] = menu_recipes_count
        dependencies['weekly_menu_recipes'] = weekly_menu_recipes_count
        dependencies['consumption_plans'] = consumption_plans_count

        # 确定是否可以删除
        if food_samples_count > 0:
            dependencies['can_delete'] = False
            dependencies['message'] = f'该食谱关联了 {food_samples_count} 条留样记录，删除会影响食品安全追溯'
        elif menu_recipes_count > 0:
            dependencies['can_delete'] = False
            dependencies['message'] = f'该食谱关联了 {menu_recipes_count} 条菜单计划，删除会影响菜单管理'
        elif weekly_menu_recipes_count > 0:
            dependencies['can_delete'] = False
            dependencies['message'] = f'该食谱关联了 {weekly_menu_recipes_count} 条周菜单，删除会影响菜单计划'
        elif consumption_plans_count > 0:
            dependencies['can_delete'] = False
            dependencies['message'] = f'该食谱关联了 {consumption_plans_count} 条消耗计划，删除会影响库存溯源'

        current_app.logger.info(f'食谱依赖检查 - ID: {recipe_id}, 结果: {dependencies}')

    except Exception as e:
        current_app.logger.error(f'检查食谱依赖关系失败: {str(e)}')
        dependencies['can_delete'] = False
        dependencies['message'] = f'检查依赖关系时出错: {str(e)}'

    return dependencies


def delete_recipe_safely(recipe):
    """安全删除食谱 - 分步进行SQL操作"""
    delete_details = {
        'recipe_ingredients_deleted': 0,
        'recipe_processes_deleted': 0,
        'process_ingredients_deleted': 0,
        'image_deleted': False
    }

    try:
        current_app.logger.info(f'开始安全删除食谱 - ID: {recipe.id}, 名称: {recipe.name}')

        # 第1步：删除工序食材关联
        process_ingredients_count = db.session.execute(
            text("""
                DELETE FROM recipe_process_ingredients
                WHERE process_id IN (
                    SELECT id FROM recipe_processes WHERE recipe_id = :recipe_id
                )
            """),
            {'recipe_id': recipe.id}
        ).rowcount
        delete_details['process_ingredients_deleted'] = process_ingredients_count
        current_app.logger.info(f'删除工序食材关联: {process_ingredients_count} 条')

        # 第2步：删除食谱工序
        processes_count = db.session.execute(
            text("DELETE FROM recipe_processes WHERE recipe_id = :recipe_id"),
            {'recipe_id': recipe.id}
        ).rowcount
        delete_details['recipe_processes_deleted'] = processes_count
        current_app.logger.info(f'删除食谱工序: {processes_count} 条')

        # 第3步：删除食谱食材关联
        ingredients_count = db.session.execute(
            text("DELETE FROM recipe_ingredients WHERE recipe_id = :recipe_id"),
            {'recipe_id': recipe.id}
        ).rowcount
        delete_details['recipe_ingredients_deleted'] = ingredients_count
        current_app.logger.info(f'删除食谱食材关联: {ingredients_count} 条')

        # 第4步：删除图片文件
        if recipe.main_image:
            try:
                file_path = os.path.join(current_app.static_folder, recipe.main_image)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    delete_details['image_deleted'] = True
                    current_app.logger.info(f'删除图片文件: {file_path}')
            except Exception as e:
                current_app.logger.error(f"删除食谱图片失败: {str(e)}")
                # 继续执行，不因图片删除失败而中断整个操作

        # 第5步：删除食谱主记录
        db.session.execute(
            text("DELETE FROM recipes WHERE id = :recipe_id"),
            {'recipe_id': recipe.id}
        )
        current_app.logger.info(f'删除食谱主记录: {recipe.id}')

        # 提交事务
        db.session.commit()
        current_app.logger.info(f'食谱删除成功 - ID: {recipe.id}')

        return {
            'success': True,
            'message': '食谱删除成功',
            'details': delete_details
        }

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除食谱失败: {str(e)}')
        return {
            'success': False,
            'message': f'删除失败: {str(e)}',
            'details': delete_details
        }

@recipe_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看食谱详情"""
    recipe = Recipe.query.get_or_404(id)

    # 获取食谱的工序和食材
    recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()
    recipe_processes = RecipeProcess.query.filter_by(recipe_id=recipe.id).order_by(RecipeProcess.process_order).all()

    # 为每个工序获取其食材
    process_ingredients = {}
    for process in recipe_processes:
        process_ingredients[process.id] = RecipeProcessIngredient.query.filter_by(process_id=process.id).all()

    # 添加审计日志
    log_activity(
        action='view',
        resource_type='Recipe',
        resource_id=recipe.id
    )

    return render_template('recipe/view.html',
                          recipe=recipe,
                          recipe_ingredients=recipe_ingredients,
                          recipe_processes=recipe_processes,
                          process_ingredients=process_ingredients)

@recipe_bp.route('/copy/<int:id>', methods=['POST'])
@login_required
def copy_recipe(id):
    """复制系统食谱为学校专用食谱"""
    try:
        # 获取原食谱
        original_recipe = Recipe.query.get_or_404(id)

        # 检查是否为系统食谱（全局食谱或area_id为空的食谱）
        # 允许复制：1. is_global=True的食谱  2. area_id为空的食谱
        if not original_recipe.is_global and original_recipe.area_id is not None:
            return jsonify({
                'success': False,
                'message': '只能复制系统食谱，不能复制其他学校的专用食谱'
            })

        # 记录调试信息
        current_app.logger.info(f'复制食谱 - 原食谱ID: {id}, 名称: {original_recipe.name}')
        current_app.logger.info(f'原食谱属性 - is_global: {original_recipe.is_global}, area_id: {original_recipe.area_id}')

        # 获取用户的当前区域
        user_area = current_user.get_current_area()
        current_app.logger.info(f'用户区域信息 - user_area: {user_area.name if user_area else "无"}, ID: {user_area.id if user_area else "无"}')

        if not user_area:
            return jsonify({
                'success': False,
                'message': '用户未绑定学校，无法复制食谱'
            })

        # 检查是否已经复制过（包括软删除的）
        new_name = f"{original_recipe.name}（{user_area.name}版）"
        existing_copy = Recipe.query.filter(
            Recipe.area_id == user_area.id,
            Recipe.name == new_name
        ).first()

        if existing_copy:
            if existing_copy.is_soft_deleted:
                # 如果存在软删除的副本，恢复并更新内容
                current_app.logger.info(f'发现软删除的食谱副本，准备恢复并更新 - ID: {existing_copy.id}')
                return restore_and_update_recipe_copy(existing_copy, original_recipe, current_user)
            else:
                # 如果存在活跃的副本，提示用户
                return jsonify({
                    'success': False,
                    'message': '该食谱已经复制到您的学校，请勿重复复制'
                })

        # 创建新的食谱副本 - 使用原始SQL避免DATETIME2精度问题
        from sqlalchemy import text

        new_name = f"{original_recipe.name}（{user_area.name}版）"
        new_description = f"基于系统食谱《{original_recipe.name}》创建的学校专用版本。原描述：{original_recipe.description or '无'}"

        # 使用原始SQL创建食谱，不包含created_at和updated_at字段
        # 根据实际表结构调整字段
        sql = text("""
            INSERT INTO recipes
            (name, category, category_id, area_id, meal_type, main_image, description,
             status, is_user_defined, is_global, priority, created_by)
            OUTPUT inserted.id
            VALUES
            (:name, :category, :category_id, :area_id, :meal_type, :main_image, :description,
             :status, :is_user_defined, :is_global, :priority, :created_by)
        """)

        result = db.session.execute(sql, {
            'name': new_name,
            'category': original_recipe.category or '',
            'category_id': original_recipe.category_id,
            'area_id': user_area.id,
            'meal_type': original_recipe.meal_type or '',
            'main_image': original_recipe.main_image or '',
            'description': new_description,
            'status': 1,
            'is_user_defined': 1,  # 使用1而不是True，因为是bit类型
            'is_global': 0,        # 使用0而不是False，因为是bit类型
            'priority': getattr(original_recipe, 'priority', 0) or 0,
            'created_by': current_user.id
        })

        # 获取新插入记录的ID
        new_recipe_id = result.scalar()

        # 复制食材关系 - 使用原始SQL避免DATETIME2精度问题
        from app.models import RecipeIngredient
        original_ingredients = RecipeIngredient.query.filter_by(recipe_id=original_recipe.id).all()

        for orig_ingredient in original_ingredients:
            # 使用原始SQL创建食材关系，根据实际表结构
            # recipe_ingredients表：recipe_id, ingredient_id, quantity, unit
            ingredient_sql = text("""
                INSERT INTO recipe_ingredients
                (recipe_id, ingredient_id, quantity, unit)
                VALUES
                (:recipe_id, :ingredient_id, :quantity, :unit)
            """)

            db.session.execute(ingredient_sql, {
                'recipe_id': new_recipe_id,
                'ingredient_id': orig_ingredient.ingredient_id,
                'quantity': float(orig_ingredient.quantity) if orig_ingredient.quantity else 0.0,
                'unit': orig_ingredient.unit or ''
            })

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'食谱已成功复制为《{new_name}》，您可以进行个性化修改',
            'recipe_id': new_recipe_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'复制食谱失败: {str(e)}')
        current_app.logger.error(f'原食谱ID: {id}')
        current_app.logger.error(f'用户区域: {user_area.name if user_area else "无"}')

        # 根据错误类型返回更具体的错误信息
        error_message = str(e)
        if 'precision' in error_message.lower():
            error_message = '数据精度错误，请联系管理员'
        elif 'foreign key' in error_message.lower():
            error_message = '数据关联错误，请检查食材信息'
        elif 'duplicate' in error_message.lower():
            error_message = '该食谱已存在，请勿重复复制'
        else:
            error_message = f'复制失败: {error_message}'

        return jsonify({
            'success': False,
            'message': error_message
        })

@recipe_bp.route('/api')
@login_required
def api_list():
    """食谱API - 实现学校级数据隔离"""
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食谱和全局食谱
    query = Recipe.query.filter(
        db.or_(
            Recipe.area_id.in_(area_ids),  # 用户学校的食谱
            Recipe.is_global == True,      # 全局食谱（系统预设）
            Recipe.area_id.is_(None)       # 兼容旧数据（无area_id的食谱）
        )
    )

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Recipe.name.like(f'%{keyword}%'))

    recipes = query.all()
    return jsonify([recipe.to_dict() for recipe in recipes])
