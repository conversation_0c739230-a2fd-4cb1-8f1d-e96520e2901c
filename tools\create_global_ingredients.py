#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建全局食材脚本
直接创建一些基础的全局食材供测试使用
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app import create_app, db
from sqlalchemy import text

def create_global_ingredients():
    """创建全局食材"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🍎 创建全局食材...")
            
            # 预定义的基础食材
            ingredients = [
                {"name": "大米", "category": "主食"},
                {"name": "面粉", "category": "主食"},
                {"name": "食用油", "category": "调料"},
                {"name": "盐", "category": "调料"},
                {"name": "糖", "category": "调料"},
                {"name": "酱油", "category": "调料"},
                {"name": "醋", "category": "调料"},
                {"name": "西红柿", "category": "蔬菜"},
                {"name": "土豆", "category": "蔬菜"},
                {"name": "胡萝卜", "category": "蔬菜"},
                {"name": "白菜", "category": "蔬菜"},
                {"name": "猪肉", "category": "肉类"},
                {"name": "牛肉", "category": "肉类"},
                {"name": "鸡肉", "category": "肉类"},
                {"name": "鸡蛋", "category": "蛋类"},
                {"name": "牛奶", "category": "奶制品"},
                {"name": "豆腐", "category": "豆制品"},
                {"name": "苹果", "category": "水果"},
                {"name": "香蕉", "category": "水果"},
                {"name": "橙子", "category": "水果"}
            ]
            
            # 首先创建分类
            categories = set(item["category"] for item in ingredients)
            for category_name in categories:
                # 检查分类是否存在
                existing = db.session.execute(text(
                    "SELECT id FROM ingredient_categories WHERE name = :name"
                ), {"name": category_name}).fetchone()
                
                if not existing:
                    # 创建分类
                    db.session.execute(text("""
                        INSERT INTO ingredient_categories (name, description, created_at, updated_at)
                        VALUES (:name, :description, GETDATE(), GETDATE())
                    """), {
                        "name": category_name,
                        "description": f"{category_name}类食材"
                    })
                    print(f"✅ 创建分类: {category_name}")
            
            db.session.commit()
            
            # 创建食材
            created_count = 0
            skipped_count = 0
            
            for item in ingredients:
                # 检查食材是否已存在
                existing = db.session.execute(text(
                    "SELECT id FROM ingredients WHERE name = :name"
                ), {"name": item["name"]}).fetchone()
                
                if existing:
                    print(f"⏭️ 跳过已存在: {item['name']}")
                    skipped_count += 1
                    continue
                
                # 获取分类ID
                category_result = db.session.execute(text(
                    "SELECT id FROM ingredient_categories WHERE name = :name"
                ), {"name": item["category"]}).fetchone()
                
                if not category_result:
                    print(f"❌ 分类不存在: {item['category']}")
                    continue
                
                category_id = category_result[0]
                
                # 创建食材
                db.session.execute(text("""
                    INSERT INTO ingredients 
                    (name, category, category_id, area_id, unit, is_global, status, created_at, updated_at)
                    VALUES 
                    (:name, :category, :category_id, NULL, '份', 1, 1, GETDATE(), GETDATE())
                """), {
                    "name": item["name"],
                    "category": item["category"],
                    "category_id": category_id
                })
                
                print(f"✅ 创建食材: {item['name']} ({item['category']})")
                created_count += 1
            
            db.session.commit()
            
            print(f"\n📊 创建完成:")
            print(f"✅ 成功创建: {created_count} 个食材")
            print(f"⏭️ 跳过重复: {skipped_count} 个食材")
            print(f"🌐 所有食材都是全局的，所有学校都可以使用")
            
            # 验证结果
            total_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients WHERE is_global = 1")).fetchone()
            total_global = total_result[0] if total_result else 0
            print(f"📈 数据库中现有全局食材: {total_global} 个")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 创建失败: {e}")

if __name__ == "__main__":
    print("🍎 **全局食材创建工具**")
    print("="*50)
    print("此工具将创建基础的全局食材供所有学校使用")
    print("="*50)
    
    create_global_ingredients()
    
    print("\n✅ 完成！现在可以访问 http://127.0.0.1:5000/ingredient/ 查看食材")
