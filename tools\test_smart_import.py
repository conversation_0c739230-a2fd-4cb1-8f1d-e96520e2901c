#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能导入功能
验证优化后的食材导入逻辑
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from PIL import Image

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory
    from tools.ingredient_batch_importer import IngredientBatchImporter
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_smart_import():
    """测试智能导入功能"""
    print("🧪 **测试智能导入功能**")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        # 创建测试环境
        test_folder = Path("test_smart_import")
        if test_folder.exists():
            shutil.rmtree(test_folder)
        test_folder.mkdir()
        
        try:
            # 1. 创建测试图片
            print("📷 创建测试图片...")
            test_images = [
                {"name": "西红柿", "category": "蔬菜", "color": (255, 100, 100)},
                {"name": "土豆", "category": "蔬菜", "color": (139, 69, 19)},
                {"name": "苹果", "category": "水果", "color": (255, 0, 0)}
            ]
            
            for item in test_images:
                # 创建分类文件夹
                category_folder = test_folder / item["category"]
                category_folder.mkdir(exist_ok=True)
                
                # 创建图片
                img = Image.new('RGB', (800, 600), item["color"])
                image_path = category_folder / f"{item['name']}.jpg"
                img.save(image_path, 'JPEG', quality=95)
                print(f"  ✅ 创建: {item['name']}.jpg")
            
            # 2. 预先在数据库中创建一个没有图片的食材
            print("\n📝 预创建测试食材...")
            sql = text("""
                INSERT INTO ingredients 
                (name, category, unit, is_global, status, created_at, updated_at)
                VALUES 
                ('西红柿', '蔬菜', '份', 1, 1, GETDATE(), GETDATE())
            """)
            db.session.execute(sql)
            db.session.commit()
            print("  ✅ 预创建食材: 西红柿 (无图片)")
            
            # 3. 测试智能导入
            print("\n🚀 开始智能导入测试...")
            importer = IngredientBatchImporter(
                source_folder=str(test_folder),
                target_folder=Path(project_root) / "app" / "static" / "uploads" / "test_smart"
            )
            
            # 扫描文件夹
            scan_result = importer.scan_source_folder()
            print(f"📊 扫描到 {len(scan_result['image_files'])} 个图片文件")
            
            # 执行导入
            report = importer.batch_import(scan_result, area_id=None, batch_size=10)
            
            # 显示结果
            importer.print_import_report(report)
            
            # 4. 验证结果
            print("\n🔍 验证导入结果...")
            
            # 检查西红柿是否有图片了
            tomato = Ingredient.query.filter_by(name='西红柿').first()
            if tomato and tomato.base_image:
                print("  ✅ 西红柿已添加图片")
            else:
                print("  ❌ 西红柿图片添加失败")
            
            # 检查其他食材是否创建
            potato = Ingredient.query.filter_by(name='土豆').first()
            apple = Ingredient.query.filter_by(name='苹果').first()
            
            if potato:
                print("  ✅ 土豆已创建")
            else:
                print("  ❌ 土豆创建失败")
                
            if apple:
                print("  ✅ 苹果已创建")
            else:
                print("  ❌ 苹果创建失败")
            
            # 统计结果
            summary = report['summary']
            print(f"\n📊 测试结果总结:")
            print(f"  🆕 新建食材: {summary['new_ingredients_created']}")
            print(f"  📷 添加图片: {summary['images_added_to_existing']}")
            print(f"  🔄 替换图片: {summary['images_replaced_for_existing']}")
            print(f"  ✅ 总成功数: {summary['total_successful_operations']}")
            print(f"  ❌ 错误数: {summary['errors_occurred']}")
            
            # 期望结果：1个添加图片，2个新建食材
            expected_added = 1  # 西红柿添加图片
            expected_created = 2  # 土豆和苹果新建
            
            if (summary['images_added_to_existing'] == expected_added and 
                summary['new_ingredients_created'] == expected_created and
                summary['errors_occurred'] == 0):
                print("\n🎉 智能导入测试通过！")
                return True
            else:
                print("\n❌ 智能导入测试失败！")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            return False
            
        finally:
            # 清理测试数据
            print("\n🧹 清理测试数据...")
            try:
                # 删除测试食材
                test_ingredients = ['西红柿', '土豆', '苹果']
                for name in test_ingredients:
                    ingredient = Ingredient.query.filter_by(name=name).first()
                    if ingredient:
                        db.session.delete(ingredient)
                db.session.commit()
                print("  ✅ 清理数据库记录")
                
                # 删除测试文件夹
                if test_folder.exists():
                    shutil.rmtree(test_folder)
                print("  ✅ 清理测试文件")
                    
            except Exception as e:
                print(f"  ⚠️ 清理过程中出现错误: {e}")

def main():
    """主函数"""
    print("🍎 **智能导入功能测试**")
    print("="*60)
    print("此测试将验证优化后的食材导入逻辑:")
    print("1. 已存在食材 + 无图片 → 添加图片")
    print("2. 不存在食材 → 创建新食材")
    print("3. 已存在食材 + 有图片 → 跳过或替换")
    print("="*60)
    
    success = test_smart_import()
    
    if success:
        print("\n✅ 所有测试通过！智能导入功能正常工作。")
    else:
        print("\n❌ 测试失败，请检查导入逻辑。")

if __name__ == "__main__":
    main()
