#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食材隔离修复工具
解决食材学校级隔离导致的显示问题

功能:
1. 将现有食材转换为全局食材
2. 修复食材的隔离设置
3. 提供不同的隔离策略选择
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, AdministrativeArea
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class IngredientIsolationFixer:
    """食材隔离修复器"""
    
    def __init__(self):
        self.app = create_app()
        
    def analyze_current_situation(self):
        """分析当前食材隔离情况"""
        print("🔍 **分析当前食材隔离情况**")
        print("="*50)
        
        with self.app.app_context():
            try:
                # 查询总数
                total_result = db.session.execute(text('SELECT COUNT(*) as total FROM ingredients')).fetchone()
                total_count = total_result[0] if total_result else 0
                print(f"📊 总食材数量: {total_count}")
                
                if total_count == 0:
                    print("❌ 数据库中没有食材数据")
                    return
                
                # 按area_id分组统计
                print("\n📂 按学校区域分组:")
                area_result = db.session.execute(text('''
                    SELECT area_id, COUNT(*) as count 
                    FROM ingredients 
                    GROUP BY area_id 
                    ORDER BY area_id
                ''')).fetchall()
                
                for row in area_result:
                    area_id, count = row
                    if area_id:
                        # 查询学校名称
                        area_name_result = db.session.execute(text(
                            'SELECT name FROM administrative_areas WHERE id = :area_id'
                        ), {'area_id': area_id}).fetchone()
                        area_name = area_name_result[0] if area_name_result else '未知学校'
                        print(f"  🏫 学校ID {area_id} ({area_name}): {count} 个食材")
                    else:
                        print(f"  🌐 全局食材 (area_id=NULL): {count} 个食材")
                
                # 按is_global分组统计
                print("\n🌐 按全局标识分组:")
                global_result = db.session.execute(text('''
                    SELECT is_global, COUNT(*) as count 
                    FROM ingredients 
                    GROUP BY is_global
                ''')).fetchall()
                
                for row in global_result:
                    is_global, count = row
                    status = "全局食材" if is_global else "学校专用食材"
                    print(f"  {status}: {count} 个")
                
                # 显示最近的食材
                print("\n📝 最近导入的10个食材:")
                recent_result = db.session.execute(text('''
                    SELECT TOP 10 id, name, category, area_id, is_global 
                    FROM ingredients 
                    ORDER BY id DESC
                ''')).fetchall()
                
                for row in recent_result:
                    ingredient_id, name, category, area_id, is_global = row
                    area_info = f"学校ID:{area_id}" if area_id else "全局"
                    global_info = "全局" if is_global else "专用"
                    print(f"  ID:{ingredient_id} {name} ({category}) - {area_info} - {global_info}")
                
            except Exception as e:
                print(f"❌ 分析失败: {e}")

    def fix_strategy_1_all_global(self):
        """策略1: 将所有食材设置为全局食材"""
        print("\n🔧 **策略1: 将所有食材设置为全局食材**")
        print("="*50)
        
        with self.app.app_context():
            try:
                # 更新所有食材为全局
                result = db.session.execute(text('''
                    UPDATE ingredients 
                    SET is_global = 1, area_id = NULL
                    WHERE is_global = 0 OR area_id IS NOT NULL
                '''))
                
                affected_rows = result.rowcount
                db.session.commit()
                
                print(f"✅ 成功更新 {affected_rows} 个食材为全局食材")
                print("📋 所有食材现在都可以被所有学校使用")
                
            except Exception as e:
                db.session.rollback()
                print(f"❌ 更新失败: {e}")

    def fix_strategy_2_keep_isolation(self):
        """策略2: 保持隔离，但将无area_id的设为全局"""
        print("\n🔧 **策略2: 保持隔离，修复无area_id的食材**")
        print("="*50)
        
        with self.app.app_context():
            try:
                # 将area_id为NULL的食材设置为全局
                result = db.session.execute(text('''
                    UPDATE ingredients 
                    SET is_global = 1
                    WHERE area_id IS NULL AND is_global = 0
                '''))
                
                affected_rows = result.rowcount
                db.session.commit()
                
                print(f"✅ 成功将 {affected_rows} 个无归属食材设置为全局")
                print("📋 保持了学校专用食材的隔离性")
                
            except Exception as e:
                db.session.rollback()
                print(f"❌ 更新失败: {e}")

    def fix_strategy_3_convert_to_global(self):
        """策略3: 将特定学校的食材转换为全局食材"""
        print("\n🔧 **策略3: 将特定学校食材转换为全局**")
        print("="*50)
        
        with self.app.app_context():
            try:
                # 查询有食材的学校
                schools_result = db.session.execute(text('''
                    SELECT DISTINCT i.area_id, a.name, COUNT(i.id) as ingredient_count
                    FROM ingredients i
                    LEFT JOIN administrative_areas a ON i.area_id = a.id
                    WHERE i.area_id IS NOT NULL
                    GROUP BY i.area_id, a.name
                    ORDER BY ingredient_count DESC
                ''')).fetchall()
                
                if not schools_result:
                    print("📋 没有发现学校专用食材")
                    return
                
                print("📋 发现以下学校有专用食材:")
                for i, (area_id, school_name, count) in enumerate(schools_result, 1):
                    print(f"  {i}. 学校ID {area_id} ({school_name}): {count} 个食材")
                
                print("\n选择操作:")
                print("1. 将所有学校食材转为全局")
                print("2. 选择特定学校转为全局")
                print("3. 取消操作")
                
                choice = input("请选择 (1-3): ").strip()
                
                if choice == "1":
                    # 转换所有学校食材为全局
                    result = db.session.execute(text('''
                        UPDATE ingredients 
                        SET is_global = 1, area_id = NULL
                        WHERE area_id IS NOT NULL
                    '''))
                    
                    affected_rows = result.rowcount
                    db.session.commit()
                    print(f"✅ 成功将 {affected_rows} 个学校食材转换为全局食材")
                    
                elif choice == "2":
                    # 选择特定学校
                    try:
                        school_choice = int(input("请输入学校编号: ").strip())
                        if 1 <= school_choice <= len(schools_result):
                            area_id = schools_result[school_choice - 1][0]
                            school_name = schools_result[school_choice - 1][1]
                            
                            result = db.session.execute(text('''
                                UPDATE ingredients 
                                SET is_global = 1, area_id = NULL
                                WHERE area_id = :area_id
                            '''), {'area_id': area_id})
                            
                            affected_rows = result.rowcount
                            db.session.commit()
                            print(f"✅ 成功将学校 {school_name} 的 {affected_rows} 个食材转换为全局食材")
                        else:
                            print("❌ 无效的学校编号")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                        
                else:
                    print("❌ 操作已取消")
                
            except Exception as e:
                db.session.rollback()
                print(f"❌ 操作失败: {e}")

    def verify_fix_result(self):
        """验证修复结果"""
        print("\n✅ **验证修复结果**")
        print("="*50)
        
        with self.app.app_context():
            try:
                # 统计修复后的情况
                total_result = db.session.execute(text('SELECT COUNT(*) FROM ingredients')).fetchone()
                total_count = total_result[0] if total_result else 0
                
                global_result = db.session.execute(text('SELECT COUNT(*) FROM ingredients WHERE is_global = 1')).fetchone()
                global_count = global_result[0] if global_result else 0
                
                school_result = db.session.execute(text('SELECT COUNT(*) FROM ingredients WHERE is_global = 0')).fetchone()
                school_count = school_result[0] if school_result else 0
                
                print(f"📊 修复后统计:")
                print(f"  总食材数量: {total_count}")
                print(f"  全局食材: {global_count} 个 ({global_count/total_count*100:.1f}%)")
                print(f"  学校专用: {school_count} 个 ({school_count/total_count*100:.1f}%)")
                
                if global_count > 0:
                    print(f"\n✅ 修复成功！现在有 {global_count} 个全局食材可供所有学校使用")
                else:
                    print(f"\n⚠️ 警告：没有全局食材，所有学校可能都看不到食材")
                
            except Exception as e:
                print(f"❌ 验证失败: {e}")

    def run_interactive_fix(self):
        """交互式修复流程"""
        print("🍎 **食材隔离修复工具**")
        print("="*60)
        
        # 分析当前情况
        self.analyze_current_situation()
        
        print("\n" + "="*60)
        print("🔧 **选择修复策略**")
        print("="*60)
        
        print("1. 🌐 将所有食材设为全局 (推荐) - 所有学校都能使用所有食材")
        print("2. 🏫 保持隔离，修复无归属食材 - 维持学校间隔离")
        print("3. 🎯 选择性转换 - 手动选择哪些食材转为全局")
        print("4. ❌ 仅查看，不修复")
        
        choice = input("\n请选择修复策略 (1-4): ").strip()
        
        if choice == "1":
            confirm = input("⚠️ 确认将所有食材设为全局？这将允许所有学校使用所有食材 (y/N): ").strip().lower()
            if confirm == 'y':
                self.fix_strategy_1_all_global()
                self.verify_fix_result()
            else:
                print("❌ 操作已取消")
                
        elif choice == "2":
            self.fix_strategy_2_keep_isolation()
            self.verify_fix_result()
            
        elif choice == "3":
            self.fix_strategy_3_convert_to_global()
            self.verify_fix_result()
            
        elif choice == "4":
            print("📋 仅查看模式，未进行任何修改")
            
        else:
            print("❌ 无效选择")

def main():
    """主函数"""
    fixer = IngredientIsolationFixer()
    fixer.run_interactive_fix()

if __name__ == "__main__":
    main()
