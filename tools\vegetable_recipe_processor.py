#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蔬菜食谱智能处理工具
从D:\蔬菜图片中去重后添加到食谱系统，并智能补充食材信息
"""

import os
import sys
import re
import uuid
from pathlib import Path
from PIL import Image, ImageOps
from datetime import datetime
from typing import Dict, List, Optional
import json
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Recipe, RecipeCategory, Ingredient, RecipeIngredient, IngredientCategory
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class VegetableRecipeProcessor:
    def __init__(self, source_folder: str = "D:/蔬菜图片",
                 target_folder: str = None):
        """
        初始化蔬菜食谱处理器

        Args:
            source_folder: 源图片文件夹路径
            target_folder: 目标图片文件夹路径
        """
        self.source_folder = Path(source_folder)

        if target_folder is None:
            self.target_folder = Path(project_root) / "app" / "static" / "uploads" / "recipes"
        else:
            self.target_folder = Path(target_folder)

        # 图片处理参数
        self.target_size = (400, 400)
        self.quality = 85

        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.JPG', '.JPEG'}

        # 统计信息
        self.stats = {
            'total_images': 0,
            'unique_recipes': 0,
            'created_recipes': 0,
            'updated_recipes': 0,
            'added_ingredients': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }

        self.errors = []
        self.processed_recipes = []
        self.duplicate_groups = defaultdict(list)

        # 文件名清理规则
        self.name_cleaning_patterns = [
            (r'^vegetable_\d+_', ''),  # 去除vegetable_序号_前缀
            (r'^\d+\.?\s*', ''),       # 去除开头数字
            (r'^\d+[-_]\s*', ''),      # 去除开头数字和分隔符
            (r'\([^)]*\)', ''),        # 去除括号内容
            (r'\[[^\]]*\]', ''),       # 去除方括号内容
            (r'（[^）]*）', ''),        # 去除中文括号内容
            (r'[_\-\s]*副本\d*$', ''), # 去除副本后缀
            (r'[_\-\s]*copy\d*$', ''), # 去除copy后缀
            (r'[_\-\s]*备份\d*$', ''), # 去除备份后缀
            (r'[_\-\s]*\d+$', ''),     # 去除结尾数字
            (r'[_\-]+', ''),           # 去除分隔符
            (r'\s+', ''),              # 去除空格
        ]

        # 蔬菜食材智能识别词典
        self.vegetable_ingredients = {
            # 叶菜类
            '青菜': ['上海青', '小白菜', '青菜'],
            '白菜': ['大白菜', '白菜', '娃娃菜'],
            '菠菜': ['菠菜'],
            '韭菜': ['韭菜', '韭黄'],
            '芹菜': ['芹菜', '西芹'],
            '生菜': ['生菜', '球生菜'],
            '苋菜': ['红苋菜', '苋菜'],
            '包菜': ['卷心菜', '包菜', '圆白菜'],
            '花菜': ['花菜', '菜花', '花椰菜'],
            '西兰花': ['西兰花', '绿花菜'],

            # 根茎类
            '山药': ['山药', '淮山'],
            '土豆': ['土豆', '马铃薯'],
            '萝卜': ['白萝卜', '萝卜'],
            '胡萝卜': ['胡萝卜', '红萝卜'],
            '红薯': ['红薯', '地瓜'],
            '芋头': ['芋头', '芋艿'],
            '莲藕': ['莲藕', '藕'],
            '竹笋': ['竹笋', '笋', '冬笋', '春笋'],
            '洋葱': ['洋葱'],
            '大葱': ['大葱', '葱'],
            '蒜': ['大蒜', '蒜'],
            '蒜苔': ['蒜苔', '蒜台'],
            '生姜': ['生姜', '姜'],

            # 瓜果类
            '茄子': ['茄子', '紫茄'],
            '冬瓜': ['冬瓜'],
            '南瓜': ['南瓜', '倭瓜'],
            '丝瓜': ['丝瓜'],
            '苦瓜': ['苦瓜'],
            '黄瓜': ['黄瓜', '青瓜'],
            '西葫芦': ['西葫芦'],
            '青椒': ['青椒', '辣椒', '尖椒'],
            '彩椒': ['彩椒', '甜椒', '红椒', '黄椒'],
            '西红柿': ['西红柿', '番茄'],

            # 豆类蔬菜
            '豆角': ['豆角', '四季豆', '刀豆'],
            '豇豆': ['豇豆', '长豆角'],
            '扁豆': ['扁豆'],
            '豆芽': ['豆芽', '绿豆芽', '黄豆芽'],
            '豌豆': ['豌豆', '荷兰豆'],
            '毛豆': ['毛豆'],

            # 菌菇类
            '蘑菇': ['蘑菇', '口蘑'],
            '香菇': ['香菇'],
            '平菇': ['平菇'],
            '杏鲍菇': ['杏鲍菇'],
            '金针菇': ['金针菇'],
            '茶树菇': ['茶树菇'],
            '草菇': ['草菇'],
            '木耳': ['木耳', '黑木耳'],
            '银耳': ['银耳', '白木耳'],

            # 其他蔬菜
            '豆腐': ['豆腐', '嫩豆腐', '老豆腐'],
            '豆干': ['豆干', '香干', '豆腐干'],
            '腐竹': ['腐竹'],
            '豆皮': ['豆皮', '千张'],
            '海带': ['海带'],
            '紫菜': ['紫菜'],
            '莼菜': ['莼菜'],
        }

        # 调料和配菜
        self.common_seasonings = [
            '食用油', '盐', '生抽', '老抽', '料酒', '醋', '糖', '味精', '鸡精',
            '胡椒粉', '花椒', '八角', '桂皮', '香叶', '葱', '姜', '蒜',
            '豆瓣酱', '郫县豆瓣', '蚝油', '芝麻油', '香油', '辣椒油'
        ]

    def normalize_recipe_name(self, filename: str) -> str:
        """
        标准化食谱名称

        Args:
            filename: 原始文件名

        Returns:
            str: 标准化后的食谱名称
        """
        if not filename:
            return ''

        # 去除扩展名
        name = Path(filename).stem

        # 应用清理规则
        for pattern, replacement in self.name_cleaning_patterns:
            name = re.sub(pattern, replacement, name, flags=re.IGNORECASE)

        return name.strip()

    def group_duplicate_recipes(self, image_files: List[Path]) -> Dict[str, List[Path]]:
        """
        按食谱名称分组，识别重复的食谱

        Args:
            image_files: 图片文件列表

        Returns:
            Dict[str, List[Path]]: 分组后的食谱字典
        """
        recipe_groups = defaultdict(list)

        for image_file in image_files:
            recipe_name = self.normalize_recipe_name(image_file.name)
            if recipe_name:  # 只处理有效的食谱名称
                recipe_groups[recipe_name].append(image_file)

        return dict(recipe_groups)

    def select_best_image(self, image_files: List[Path]) -> Path:
        """
        从多个重复图片中选择最佳的一个

        Args:
            image_files: 同一食谱的图片文件列表

        Returns:
            Path: 最佳图片文件
        """
        if len(image_files) == 1:
            return image_files[0]

        # 选择策略：文件大小适中且清晰度好的图片
        best_image = None
        best_score = 0

        for image_file in image_files:
            try:
                file_size = image_file.stat().st_size

                # 计算评分：文件大小在20KB-100KB之间得分较高
                size_score = 0
                if 20000 <= file_size <= 100000:
                    size_score = 1.0
                elif 10000 <= file_size <= 200000:
                    size_score = 0.8
                else:
                    size_score = 0.5

                # 文件名越短越好（去除冗余信息）
                name_score = 1.0 / (len(image_file.name) / 50 + 1)

                total_score = size_score * 0.7 + name_score * 0.3

                if total_score > best_score:
                    best_score = total_score
                    best_image = image_file

            except Exception as e:
                print(f"⚠️ 评估图片失败 {image_file}: {e}")
                continue

        return best_image or image_files[0]

    def extract_ingredients_from_name(self, recipe_name: str) -> List[str]:
        """
        从食谱名称中智能提取食材

        Args:
            recipe_name: 食谱名称

        Returns:
            List[str]: 提取的食材列表
        """
        ingredients = []

        # 遍历蔬菜食材词典
        for main_ingredient, variants in self.vegetable_ingredients.items():
            for variant in variants:
                if variant in recipe_name:
                    ingredients.append(main_ingredient)
                    break

        # 去重并保持顺序
        unique_ingredients = []
        for ingredient in ingredients:
            if ingredient not in unique_ingredients:
                unique_ingredients.append(ingredient)

        return unique_ingredients

    def determine_recipe_category(self, recipe_name: str, ingredients: List[str]) -> str:
        """
        根据食谱名称和食材确定分类

        Args:
            recipe_name: 食谱名称
            ingredients: 食材列表

        Returns:
            str: 食谱分类
        """
        recipe_name_lower = recipe_name.lower()

        # 根据关键词判断分类
        if any(keyword in recipe_name_lower for keyword in ['汤', '羹', '煲']):
            return '汤品'
        elif any(keyword in recipe_name_lower for keyword in ['饭', '粥', '面', '粉']):
            return '主食'
        elif any(keyword in recipe_name_lower for keyword in ['豆腐', '豆干', '腐竹']):
            return '豆制品'
        elif any(ingredient in ['蘑菇', '香菇', '杏鲍菇', '木耳'] for ingredient in ingredients):
            return '菌菇类'
        else:
            return '素菜'

    def find_or_create_category(self, category_name: str) -> int:
        """
        查找或创建食谱分类

        Args:
            category_name: 分类名称

        Returns:
            int: 分类ID
        """
        category = RecipeCategory.query.filter_by(name=category_name).first()
        if not category:
            category = RecipeCategory(
                name=category_name,
                description=f'蔬菜食谱自动创建的{category_name}分类'
            )
            db.session.add(category)
            db.session.flush()

        return category.id

    def find_or_create_ingredient(self, ingredient_name: str) -> Optional[int]:
        """
        查找或创建食材

        Args:
            ingredient_name: 食材名称

        Returns:
            Optional[int]: 食材ID
        """
        # 首先查找现有食材
        ingredient = Ingredient.query.filter_by(name=ingredient_name).first()
        if ingredient:
            return ingredient.id

        # 创建新食材
        try:
            # 确定食材分类
            category_name = self.determine_ingredient_category(ingredient_name)

            # 查找或创建食材分类
            ingredient_category = IngredientCategory.query.filter_by(name=category_name).first()
            if not ingredient_category:
                ingredient_category = IngredientCategory(
                    name=category_name,
                    description=f'自动创建的{category_name}分类'
                )
                db.session.add(ingredient_category)
                db.session.flush()

            # 使用原始SQL创建食材，避免ORM精度问题
            sql = text("""
                INSERT INTO ingredients
                (name, category, category_id, unit, is_global, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :category_id, :unit, :is_global, :status, GETDATE(), GETDATE())
            """)

            params = {
                'name': ingredient_name,
                'category': category_name,
                'category_id': ingredient_category.id,
                'unit': 'g',  # 默认单位
                'is_global': True,  # 设为全局食材
                'status': 1
            }

            result = db.session.execute(sql, params)
            ingredient_id = result.fetchone()[0]

            self.stats['added_ingredients'] += 1
            print(f"  ➕ 创建新食材: {ingredient_name} ({category_name}) [ID: {ingredient_id}]")

            return ingredient_id

        except Exception as e:
            print(f"❌ 创建食材失败 {ingredient_name}: {e}")
            return None

    def determine_ingredient_category(self, ingredient_name: str) -> str:
        """
        确定食材分类

        Args:
            ingredient_name: 食材名称

        Returns:
            str: 食材分类
        """
        # 根据食材名称确定分类
        if ingredient_name in ['上海青', '小白菜', '青菜', '大白菜', '白菜', '菠菜', '韭菜', '芹菜', '生菜', '苋菜']:
            return '叶菜类'
        elif ingredient_name in ['山药', '土豆', '萝卜', '胡萝卜', '红薯', '芋头', '莲藕', '竹笋', '洋葱', '大葱', '蒜', '蒜苔']:
            return '根茎类'
        elif ingredient_name in ['茄子', '冬瓜', '南瓜', '丝瓜', '苦瓜', '黄瓜', '青椒', '彩椒', '西红柿']:
            return '瓜果类'
        elif ingredient_name in ['豆角', '豇豆', '扁豆', '豆芽', '豌豆', '毛豆']:
            return '豆类蔬菜'
        elif ingredient_name in ['蘑菇', '香菇', '杏鲍菇', '木耳', '银耳']:
            return '菌菇类'
        elif ingredient_name in ['豆腐', '豆干', '腐竹', '豆皮']:
            return '豆制品'
        else:
            return '其他蔬菜'

    def compress_and_save_image(self, source_path: Path, target_path: Path) -> bool:
        """
        压缩并保存图片

        Args:
            source_path: 源图片路径
            target_path: 目标图片路径

        Returns:
            bool: 是否成功
        """
        try:
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)

            with Image.open(source_path) as img:
                # 转换为RGB模式（处理RGBA、P等模式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # 使用高质量重采样算法调整大小
                img = ImageOps.fit(img, self.target_size, Image.Resampling.LANCZOS)

                # 保存压缩后的图片
                img.save(target_path, 'JPEG', quality=self.quality, optimize=True)

                return True

        except Exception as e:
            self.errors.append(f"图片压缩失败 {source_path}: {e}")
            return False

    def create_recipe_with_ingredients(self, recipe_name: str, image_file: Path) -> bool:
        """
        创建食谱并添加食材

        Args:
            recipe_name: 食谱名称
            image_file: 图片文件

        Returns:
            bool: 是否成功
        """
        try:
            # 检查食谱是否已存在
            existing_recipe = Recipe.query.filter_by(name=recipe_name).first()
            if existing_recipe:
                print(f"⏭️ 食谱已存在，跳过: {recipe_name}")
                return True

            # 提取食材
            ingredients = self.extract_ingredients_from_name(recipe_name)

            # 确定分类
            category = self.determine_recipe_category(recipe_name, ingredients)
            category_id = self.find_or_create_category(category)

            # 处理图片
            image_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}.jpg"
            target_image_path = self.target_folder / image_filename

            if not self.compress_and_save_image(image_file, target_image_path):
                return False

            # 使用原始SQL创建食谱
            sql = text("""
                INSERT INTO recipes
                (name, category, category_id, meal_type, description, main_image, status, is_global, is_user_defined, priority, created_by, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :category_id, :meal_type, :description, :main_image, :status, :is_global, :is_user_defined, :priority, :created_by, GETDATE(), GETDATE())
            """)

            params = {
                'name': recipe_name,
                'category': category,
                'category_id': category_id,
                'meal_type': '午餐',
                'description': f'蔬菜食谱：{recipe_name}，营养丰富的素食菜品',
                'main_image': f"uploads/recipes/{image_filename}",
                'status': 1,  # 启用
                'is_global': True,  # 设为全局食谱
                'is_user_defined': False,  # 不是用户自定义
                'priority': 80,  # 中等优先级
                'created_by': 1  # 系统用户
            }

            result = db.session.execute(sql, params)
            recipe_id = result.fetchone()[0]

            # 添加食材关联
            ingredient_count = 0
            for ingredient_name in ingredients:
                ingredient_id = self.find_or_create_ingredient(ingredient_name)
                if ingredient_id:
                    # 创建食材关联
                    recipe_ingredient = RecipeIngredient(
                        recipe_id=recipe_id,
                        ingredient_id=ingredient_id,
                        quantity=100.0,  # 默认用量100g
                        unit='g'
                    )
                    db.session.add(recipe_ingredient)
                    ingredient_count += 1

            # 添加常用调料
            for seasoning in self.common_seasonings[:5]:  # 添加前5种常用调料
                seasoning_id = self.find_or_create_ingredient(seasoning)
                if seasoning_id:
                    # 确定调料用量
                    if seasoning in ['盐', '糖', '味精', '鸡精']:
                        quantity = 5.0  # 5g
                    elif seasoning in ['生抽', '老抽', '料酒', '醋']:
                        quantity = 10.0  # 10ml
                    elif seasoning == '食用油':
                        quantity = 20.0  # 20ml
                    else:
                        quantity = 3.0  # 3g

                    recipe_ingredient = RecipeIngredient(
                        recipe_id=recipe_id,
                        ingredient_id=seasoning_id,
                        quantity=quantity,
                        unit='g' if seasoning not in ['生抽', '老抽', '料酒', '醋', '食用油'] else 'ml'
                    )
                    db.session.add(recipe_ingredient)
                    ingredient_count += 1

            self.stats['created_recipes'] += 1
            self.processed_recipes.append({
                'id': recipe_id,
                'name': recipe_name,
                'category': category,
                'ingredient_count': ingredient_count,
                'image_file': image_filename
            })

            print(f"✅ 创建食谱: {recipe_name} (ID: {recipe_id}, 食材: {ingredient_count}个)")
            return True

        except Exception as e:
            error_msg = f"创建食谱失败 {recipe_name}: {e}"
            self.errors.append(error_msg)
            self.stats['errors'] += 1
            print(f"❌ {error_msg}")
            return False

    def process_vegetable_recipes(self) -> Dict:
        """
        处理蔬菜食谱

        Returns:
            Dict: 处理报告
        """
        self.stats['start_time'] = datetime.now()

        print(f"📂 扫描源文件夹: {self.source_folder}")

        if not self.source_folder.exists():
            raise FileNotFoundError(f"源文件夹不存在: {self.source_folder}")

        # 获取所有图片文件
        image_files = []
        for ext in self.image_extensions:
            image_files.extend(self.source_folder.glob(f"*{ext}"))

        self.stats['total_images'] = len(image_files)
        print(f"🖼️ 找到图片文件: {len(image_files)} 个")

        if not image_files:
            print("❌ 没有找到图片文件")
            return self.generate_report()

        # 按食谱名称分组去重
        recipe_groups = self.group_duplicate_recipes(image_files)
        self.stats['unique_recipes'] = len(recipe_groups)

        print(f"🎯 去重后食谱数量: {len(recipe_groups)} 个")

        # 显示重复情况
        duplicates = {name: files for name, files in recipe_groups.items() if len(files) > 1}
        if duplicates:
            print(f"📊 发现重复食谱: {len(duplicates)} 个")
            for name, files in list(duplicates.items())[:5]:
                print(f"  🔄 {name}: {len(files)} 个重复图片")
            if len(duplicates) > 5:
                print(f"  ... 还有 {len(duplicates) - 5} 个重复食谱")

        # 显示一些食谱示例
        print(f"\n📋 食谱示例 (前10个):")
        for i, (recipe_name, files) in enumerate(list(recipe_groups.items())[:10], 1):
            ingredients = self.extract_ingredients_from_name(recipe_name)
            print(f"  {i}. {recipe_name} (食材: {', '.join(ingredients[:3])}{'...' if len(ingredients) > 3 else ''})")

        # 询问是否继续
        response = input(f"\n是否开始处理这 {len(recipe_groups)} 个蔬菜食谱？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return self.generate_report()

        # 处理每个食谱
        print(f"\n🚀 开始处理蔬菜食谱...")

        for i, (recipe_name, files) in enumerate(recipe_groups.items(), 1):
            progress = (i / len(recipe_groups)) * 100
            print(f"[{progress:.1f}%] 处理: {recipe_name}")

            # 选择最佳图片
            best_image = self.select_best_image(files)

            if len(files) > 1:
                print(f"  📊 从 {len(files)} 个重复图片中选择最佳: {best_image.name}")

            # 创建食谱和食材
            self.create_recipe_with_ingredients(recipe_name, best_image)

        # 提交所有更改
        try:
            db.session.commit()
            print(f"\n✅ 所有更改已提交到数据库")
        except Exception as e:
            db.session.rollback()
            error_msg = f"数据库提交失败: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")

        self.stats['end_time'] = datetime.now()

        return self.generate_report()

    def generate_report(self) -> Dict:
        """生成处理报告"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']

        report = {
            'summary': {
                'total_images_scanned': self.stats['total_images'],
                'unique_recipes_found': self.stats['unique_recipes'],
                'recipes_created': self.stats['created_recipes'],
                'recipes_updated': self.stats['updated_recipes'],
                'ingredients_added': self.stats['added_ingredients'],
                'errors_occurred': self.stats['errors'],
                'duration_seconds': duration.total_seconds() if duration else None,
                'success_rate': (self.stats['created_recipes'] / max(self.stats['unique_recipes'], 1)) * 100
            },
            'processed_recipes': self.processed_recipes,
            'errors': self.errors,
            'timestamp': datetime.now().isoformat()
        }

        # 保存报告到文件
        report_filename = f"vegetable_recipe_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            report['report_file'] = report_filename
        except Exception as e:
            print(f"⚠️ 保存报告文件失败: {e}")

        return report

    def print_process_report(self, report: Dict):
        """打印处理报告"""
        print("\n" + "="*60)
        print("🥬 **蔬菜食谱智能处理报告**")
        print("="*60)

        summary = report['summary']

        print(f"📊 处理统计:")
        print(f"  📂 扫描图片总数: {summary['total_images_scanned']}")
        print(f"  🎯 去重后食谱数: {summary['unique_recipes_found']}")
        print(f"  ✅ 成功创建食谱: {summary['recipes_created']}")
        print(f"  🔄 更新食谱数量: {summary['recipes_updated']}")
        print(f"  🥕 新增食材数量: {summary['ingredients_added']}")
        print(f"  ❌ 处理错误数量: {summary['errors_occurred']}")
        print(f"  📈 成功率: {summary['success_rate']:.1f}%")

        if summary['duration_seconds']:
            print(f"  ⏱️ 总耗时: {summary['duration_seconds']:.2f} 秒")

        # 显示创建的食谱
        if report['processed_recipes']:
            print(f"\n📋 **创建的食谱** ({len(report['processed_recipes'])} 个):")
            for i, recipe in enumerate(report['processed_recipes'][:10], 1):
                print(f"  {i}. {recipe['name']} ({recipe['category']}) - {recipe['ingredient_count']}个食材")
            if len(report['processed_recipes']) > 10:
                print(f"  ... 还有 {len(report['processed_recipes']) - 10} 个食谱")

        if report['errors']:
            print(f"\n❌ **错误详情** ({len(report['errors'])} 个):")
            for i, error in enumerate(report['errors'][:5], 1):
                print(f"  {i}. {error}")
            if len(report['errors']) > 5:
                print(f"  ... 还有 {len(report['errors']) - 5} 个错误")

        if 'report_file' in report:
            print(f"\n💾 详细报告已保存到: {report['report_file']}")

        print("\n✅ 蔬菜食谱智能处理完成！")

def main():
    """主函数"""
    print("🥬 **蔬菜食谱智能处理工具**")
    print("="*60)
    print("从D:\\蔬菜图片中去重后添加到食谱系统，并智能补充食材信息")
    print("="*60)

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 初始化处理器
        processor = VegetableRecipeProcessor()

        try:
            # 执行处理
            report = processor.process_vegetable_recipes()

            # 显示报告
            processor.print_process_report(report)

            # 显示最终统计
            if report['summary']['recipes_created'] > 0:
                print(f"\n🎉 蔬菜食谱处理成功！")
                print(f"📊 处理成果:")
                print(f"  ✅ 新增食谱: {report['summary']['recipes_created']} 个")
                print(f"  🥕 新增食材: {report['summary']['ingredients_added']} 个")
                print(f"  📈 成功率: {report['summary']['success_rate']:.1f}%")

                print(f"\n🌐 您可以访问 http://127.0.0.1:5000/recipe/ 查看新增的蔬菜食谱")

        except Exception as e:
            print(f"❌ 处理过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
