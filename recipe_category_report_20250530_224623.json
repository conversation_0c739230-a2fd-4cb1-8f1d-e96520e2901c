{"summary": {"total_recipes": 321, "total_categories": 22, "uncategorized_recipes": 0, "categories_created": 4, "recipes_updated": 150, "start_time": "2025-05-30 22:46:22.956264", "end_time": "2025-05-30 22:46:23.602476"}, "current_analysis": {"category_table_stats": {"主食": {"id": 6, "count": 0, "description": "米饭、面食等主食类"}, "荤菜": {"id": 7, "count": 0, "description": "肉类、禽类等荤菜"}, "素菜": {"id": 8, "count": 0, "description": "蔬菜类菜品"}, "汤类": {"id": 9, "count": 0, "description": "各类汤品"}, "小吃": {"id": 10, "count": 0, "description": "点心、小吃类"}, "水果": {"id": 11, "count": 0, "description": "各类水果"}, "肉类": {"id": 17, "count": 15, "description": "各种肉类菜品"}, "主食类": {"id": 18, "count": 25, "description": "米饭、面食等"}, "蔬菜类": {"id": 19, "count": 12, "description": "各种蔬菜"}, "水果类": {"id": 20, "count": 12, "description": "各种水果"}, "饮品类": {"id": 21, "count": 10, "description": "各种饮料"}, "海鲜类": {"id": 22, "count": 12, "description": "鱼、虾、蟹等海鲜"}, "小吃类": {"id": 23, "count": 15, "description": "各种小吃"}, "其他": {"id": 24, "count": 3, "description": "省厅食谱自动创建的其他分类"}, "豆制品": {"id": 27, "count": 5, "description": "蔬菜食谱自动创建的豆制品分类"}, "菌菇类": {"id": 28, "count": 25, "description": "蔬菜食谱自动创建的菌菇类分类"}, "汤品": {"id": 29, "count": 4, "description": "蔬菜食谱自动创建的汤品分类"}}, "category_field_stats": {"素菜": 144, "菌菇类": 25, "主食类": 25, "汤类": 16, "肉类": 15, "小吃类": 15, "蔬菜类": 12, "水果类": 12, "海鲜类": 12, "荤菜": 12, "主食": 11, "饮品类": 10, "豆制品": 5, "汤品": 4, "其他": 3}, "uncategorized_count": 0}, "final_stats": {"荤菜": 12, "素菜": 144, "豆制品": 5, "菌菇类": 25, "汤品": 4, "主食": 11, "凉菜": 0, "小食": 0, "海鲜": 0, "蛋类": 0, "未分类": 0}, "standard_categories": ["荤菜", "素菜", "豆制品", "菌菇类", "汤品", "主食", "凉菜", "小食", "海鲜", "蛋类"], "timestamp": "2025-05-30T22:46:23.602476"}