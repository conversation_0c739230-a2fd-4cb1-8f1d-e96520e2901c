{% extends "base.html" %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .menu-table {
        width: 100%;
        border-collapse: collapse;
    }

    .menu-table th, .menu-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .menu-table th {
        background-color: #f2f2f2;
    }

    .meal-cell {
        min-height: 100px;
        background-color: #f9f9f9;
        padding: 10px;
    }

    .recipe-item {
        margin-bottom: 5px;
        padding: 5px;
        background-color: #e9ecef;
        border-radius: 4px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }

    .status-badge.published {
        background-color: #dc3545; /* 改为红色(danger)，更加醒目 */
        color: white;
    }

    .status-badge.planning {
        background-color: #6c757d;
        color: white;
    }

    .print-button {
        margin-left: 10px;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            border-bottom: 1px solid #ddd !important;
        }

        .menu-table th {
            background-color: #f2f2f2 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <a href="{{ url_for('weekly_menu_v2.print_menu', id=weekly_menu.id) }}" class="btn btn-primary print-button" target="_blank">
                <i class="fas fa-print"></i> 打印菜单
            </a>
            {% if weekly_menu.status == '计划中' %}
                <a href="{{ url_for('weekly_menu_v2.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start) }}" class="btn btn-info">
                    <i class="fas fa-edit"></i> 编辑菜单
                </a>
            {% endif %}
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
                <i class="fas fa-list"></i> 返回列表
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                {{ weekly_menu.area.name }} 周菜单计划
                ({{ weekly_menu.week_start|format_datetime('%Y-%m-%d') }} 至 {{ weekly_menu.week_end|format_datetime('%Y-%m-%d') }})
                <span class="status-badge {% if weekly_menu.status == '已发布' %}published{% else %}planning{% endif %}">
                    {{ weekly_menu.status }}
                </span>
            </h6>
            <div class="dropdown no-arrow no-print">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">菜单操作:</div>
                    <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.copy', area_id=weekly_menu.area_id) }}">
                        <i class="fas fa-copy fa-sm fa-fw mr-2 text-gray-400"></i>
                        复制到其他周次
                    </a>
                    {% if weekly_menu.status == '已发布' %}
                        <a class="dropdown-item" href="{{ url_for('purchase_order.create_from_menu', weekly_menu_id=weekly_menu.id) }}">
                            <i class="fas fa-shopping-cart fa-sm fa-fw mr-2 text-gray-400"></i>
                            创建采购订单
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="menu-table">
                    <thead>
                        <tr>
                            <th width="15%">日期</th>
                            <th width="28%">早餐</th>
                            <th width="28%">午餐</th>
                            <th width="28%">晚餐</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for date, day_data in week_data.items() %}
                            <tr>
                                <td>{{ date }}<br>{{ day_data.weekday }}</td>
                                <td>
                                    <div class="meal-cell">
                                        {% if day_data.meals['早餐'] %}
                                            {% for recipe in day_data.meals['早餐'] %}
                                                <div class="recipe-item">{{ recipe.name }}</div>
                                            {% endfor %}
                                        {% else %}
                                            <em>无菜品</em>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="meal-cell">
                                        {% if day_data.meals['午餐'] %}
                                            {% for recipe in day_data.meals['午餐'] %}
                                                <div class="recipe-item">{{ recipe.name }}</div>
                                            {% endfor %}
                                        {% else %}
                                            <em>无菜品</em>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="meal-cell">
                                        {% if day_data.meals['晚餐'] %}
                                            {% for recipe in day_data.meals['晚餐'] %}
                                                <div class="recipe-item">{{ recipe.name }}</div>
                                            {% endfor %}
                                        {% else %}
                                            <em>无菜品</em>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <p><strong>创建者:</strong> {{ weekly_menu.creator.name if weekly_menu.creator else '未知' }}</p>
                <p><strong>创建时间:</strong> {{ weekly_menu.created_at|format_datetime }}</p>
                <p><strong>更新时间:</strong> {{ weekly_menu.updated_at|format_datetime }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
