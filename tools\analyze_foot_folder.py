#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析D:\FOOT文件夹结构工具
用于了解食材数据的组织方式
"""

import os
import json
from pathlib import Path
from collections import defaultdict

def analyze_foot_folder(folder_path="D:\\FOOT"):
    """分析FOOT文件夹结构"""
    
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return None
    
    analysis = {
        'total_files': 0,
        'total_dirs': 0,
        'file_types': defaultdict(int),
        'image_files': [],
        'text_files': [],
        'other_files': [],
        'directory_structure': {},
        'potential_ingredients': []
    }
    
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}
    text_extensions = {'.txt', '.csv', '.json', '.xml', '.xlsx', '.xls'}
    
    print(f"🔍 开始分析文件夹: {folder_path}")
    print("=" * 60)
    
    # 遍历文件夹
    for root, dirs, files in os.walk(folder_path):
        rel_path = os.path.relpath(root, folder_path)
        analysis['total_dirs'] += len(dirs)
        
        # 记录目录结构
        if rel_path not in analysis['directory_structure']:
            analysis['directory_structure'][rel_path] = {
                'subdirs': dirs.copy(),
                'files': [],
                'file_count': len(files)
            }
        
        for file in files:
            analysis['total_files'] += 1
            file_path = os.path.join(root, file)
            file_ext = Path(file).suffix.lower()
            file_name = Path(file).stem
            
            # 统计文件类型
            analysis['file_types'][file_ext] += 1
            
            # 分类文件
            file_info = {
                'name': file,
                'path': file_path,
                'relative_path': os.path.relpath(file_path, folder_path),
                'size': os.path.getsize(file_path),
                'directory': rel_path
            }
            
            if file_ext in image_extensions:
                analysis['image_files'].append(file_info)
                # 可能的食材名称（从文件名推断）
                potential_name = file_name.replace('_', ' ').replace('-', ' ')
                analysis['potential_ingredients'].append({
                    'name': potential_name,
                    'image_path': file_path,
                    'source': 'image_filename'
                })
            elif file_ext in text_extensions:
                analysis['text_files'].append(file_info)
            else:
                analysis['other_files'].append(file_info)
            
            # 记录到目录结构中
            analysis['directory_structure'][rel_path]['files'].append(file_info)
    
    return analysis

def print_analysis_report(analysis):
    """打印分析报告"""
    if not analysis:
        return
    
    print("📊 **文件夹分析报告**")
    print("=" * 60)
    
    # 基本统计
    print(f"📁 总目录数: {analysis['total_dirs']}")
    print(f"📄 总文件数: {analysis['total_files']}")
    print()
    
    # 文件类型统计
    print("📋 **文件类型分布**")
    print("-" * 30)
    for ext, count in sorted(analysis['file_types'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {ext or '无扩展名'}: {count} 个")
    print()
    
    # 图片文件统计
    print(f"🖼️ **图片文件**: {len(analysis['image_files'])} 个")
    if analysis['image_files']:
        print("前10个图片文件:")
        for i, img in enumerate(analysis['image_files'][:10]):
            size_mb = img['size'] / (1024 * 1024)
            print(f"  {i+1}. {img['name']} ({size_mb:.2f}MB)")
        if len(analysis['image_files']) > 10:
            print(f"  ... 还有 {len(analysis['image_files']) - 10} 个图片文件")
    print()
    
    # 文本文件统计
    print(f"📝 **文本文件**: {len(analysis['text_files'])} 个")
    if analysis['text_files']:
        for txt in analysis['text_files'][:5]:
            print(f"  - {txt['name']}")
        if len(analysis['text_files']) > 5:
            print(f"  ... 还有 {len(analysis['text_files']) - 5} 个文本文件")
    print()
    
    # 目录结构
    print("📂 **目录结构**")
    print("-" * 30)
    for dir_path, info in analysis['directory_structure'].items():
        if dir_path == '.':
            print(f"📁 根目录 ({info['file_count']} 个文件)")
        else:
            print(f"📁 {dir_path} ({info['file_count']} 个文件)")
        
        # 显示子目录
        for subdir in info['subdirs'][:3]:
            print(f"  📂 {subdir}/")
        if len(info['subdirs']) > 3:
            print(f"  📂 ... 还有 {len(info['subdirs']) - 3} 个子目录")
    print()
    
    # 潜在食材
    print(f"🥬 **潜在食材**: {len(analysis['potential_ingredients'])} 个")
    if analysis['potential_ingredients']:
        print("前10个潜在食材:")
        for i, ingredient in enumerate(analysis['potential_ingredients'][:10]):
            print(f"  {i+1}. {ingredient['name']}")
        if len(analysis['potential_ingredients']) > 10:
            print(f"  ... 还有 {len(analysis['potential_ingredients']) - 10} 个潜在食材")

def save_analysis_to_file(analysis, output_file="foot_analysis.json"):
    """保存分析结果到文件"""
    if not analysis:
        return
    
    # 转换为可序列化的格式
    serializable_analysis = {}
    for key, value in analysis.items():
        if isinstance(value, defaultdict):
            serializable_analysis[key] = dict(value)
        else:
            serializable_analysis[key] = value
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(serializable_analysis, f, ensure_ascii=False, indent=2)
    
    print(f"💾 分析结果已保存到: {output_file}")

def main():
    """主函数"""
    print("🔍 **FOOT文件夹分析工具**")
    print("=" * 60)
    
    # 分析文件夹
    analysis = analyze_foot_folder()
    
    if analysis:
        # 打印报告
        print_analysis_report(analysis)
        
        # 保存结果
        save_analysis_to_file(analysis)
        
        print("\n✅ 分析完成！")
        print("\n💡 **建议下一步操作**:")
        print("1. 检查分析结果文件 foot_analysis.json")
        print("2. 确认食材数据的组织方式")
        print("3. 运行食材导入工具")
    else:
        print("❌ 分析失败，请检查文件夹路径")

if __name__ == "__main__":
    main()
