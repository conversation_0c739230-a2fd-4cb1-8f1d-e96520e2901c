#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的导入测试
逐步测试导入功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory
    from tools.ingredient_batch_importer import IngredientBatchImporter
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_import_single_file():
    """测试导入单个文件"""
    print("🧪 **测试导入单个文件**")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 选择第一个图片文件
            source_folder = Path("D:/FOOT")
            image_files = list(source_folder.glob("*.jpg"))
            
            if not image_files:
                print("❌ 源文件夹中没有找到jpg文件")
                return False
            
            test_file = image_files[0]
            print(f"📷 测试文件: {test_file.name}")
            
            # 2. 初始化导入器
            importer = IngredientBatchImporter()
            
            # 3. 检查前提条件
            if not importer.check_prerequisites():
                print("❌ 前提条件检查失败")
                return False
            
            # 4. 构造图片信息
            image_info = {
                'file_path': test_file,
                'category': '其他',
                'ingredient_name': test_file.stem,
                'size': test_file.stat().st_size
            }
            
            print(f"📝 食材信息:")
            print(f"  名称: {image_info['ingredient_name']}")
            print(f"  分类: {image_info['category']}")
            print(f"  大小: {image_info['size']:,} 字节")
            
            # 5. 处理单个食材
            print(f"\n🚀 开始处理...")
            success = importer.process_single_ingredient(image_info)
            
            if success:
                # 提交事务
                db.session.commit()
                print("✅ 事务提交成功")
                
                # 6. 验证结果
                print(f"\n🔍 验证结果...")
                
                # 检查数据库记录
                ingredient = Ingredient.query.filter_by(name=image_info['ingredient_name']).first()
                if ingredient:
                    print(f"✅ 数据库记录: ID={ingredient.id}, 名称={ingredient.name}")
                    print(f"📂 图片路径: {ingredient.base_image}")
                    
                    # 检查图片文件
                    if ingredient.base_image:
                        image_file_path = Path(project_root) / "app" / "static" / ingredient.base_image
                        if image_file_path.exists():
                            file_size = image_file_path.stat().st_size
                            print(f"✅ 图片文件存在: {image_file_path.name}")
                            print(f"📏 文件大小: {file_size:,} 字节")
                        else:
                            print(f"❌ 图片文件不存在: {image_file_path}")
                    else:
                        print("❌ 数据库中没有图片路径")
                else:
                    print("❌ 数据库中未找到记录")
                    return False
                
                print("\n✅ 单文件导入测试成功！")
                return True
            else:
                db.session.rollback()
                print("❌ 处理失败")
                return False
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ 测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_import_batch():
    """测试批量导入（少量文件）"""
    print("\n🧪 **测试批量导入（前5个文件）**")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 初始化导入器
            importer = IngredientBatchImporter()
            
            # 2. 扫描源文件夹
            scan_result = importer.scan_source_folder()
            
            # 3. 只处理前5个文件
            limited_scan_result = {
                'total_files': scan_result['total_files'],
                'image_files': scan_result['image_files'][:5],  # 只取前5个
                'categories': scan_result['categories'],
                'potential_ingredients': scan_result['potential_ingredients'][:5]
            }
            
            print(f"📊 限制处理文件数: {len(limited_scan_result['image_files'])}")
            
            # 4. 执行批量导入
            report = importer.batch_import(limited_scan_result, batch_size=5)
            
            # 5. 显示报告
            importer.print_import_report(report)
            
            # 6. 验证结果
            print(f"\n🔍 验证ingredients文件夹...")
            ingredients_folder = Path(project_root) / "app" / "static" / "uploads" / "ingredients"
            files = list(ingredients_folder.glob("*"))
            print(f"📄 文件数量: {len(files)}")
            
            if files:
                print("📝 文件列表:")
                for i, file in enumerate(files, 1):
                    file_size = file.stat().st_size
                    print(f"  {i}. {file.name} ({file_size:,} 字节)")
            
            return len(files) > 0
            
        except Exception as e:
            print(f"❌ 批量导入测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def check_database_status():
    """检查数据库状态"""
    print("\n📊 **检查数据库状态**")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查食材数量
            ingredient_count = Ingredient.query.count()
            print(f"🗄️ 食材总数: {ingredient_count}")
            
            # 检查有图片的食材数量
            with_image_count = Ingredient.query.filter(Ingredient.base_image.isnot(None)).count()
            print(f"📷 有图片的食材: {with_image_count}")
            
            # 检查分类数量
            category_count = IngredientCategory.query.count()
            print(f"🏷️ 分类总数: {category_count}")
            
            # 显示最近的几个食材
            recent_ingredients = Ingredient.query.order_by(Ingredient.id.desc()).limit(5).all()
            if recent_ingredients:
                print(f"\n📝 最近的食材:")
                for i, ingredient in enumerate(recent_ingredients, 1):
                    has_image = "有图片" if ingredient.base_image else "无图片"
                    print(f"  {i}. {ingredient.name} ({ingredient.category}) - {has_image}")
            
        except Exception as e:
            print(f"❌ 检查数据库状态失败: {e}")

def main():
    """主函数"""
    print("🍎 **简化导入测试工具**")
    print("="*60)
    
    # 检查数据库状态
    check_database_status()
    
    # 测试单文件导入
    single_success = test_import_single_file()
    
    if single_success:
        print("\n🎉 单文件导入成功！继续测试批量导入...")
        
        # 测试批量导入
        batch_success = test_import_batch()
        
        if batch_success:
            print("\n🎉 批量导入测试成功！")
            print("\n💡 **下一步建议**:")
            print("1. 访问 http://127.0.0.1:5000/ingredient/ 查看导入的食材")
            print("2. 如果测试成功，可以运行完整的批量导入:")
            print("   python tools/ingredient_batch_importer.py")
        else:
            print("\n❌ 批量导入测试失败")
    else:
        print("\n❌ 单文件导入测试失败，请检查配置")

if __name__ == "__main__":
    main()
