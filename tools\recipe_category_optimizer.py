#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食谱分类优化工具
分析当前食谱分类情况，完善分类体系，优化分类数据
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Optional
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Recipe, RecipeCategory
    from sqlalchemy import text, func
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class RecipeCategoryOptimizer:
    def __init__(self):
        """初始化食谱分类优化器"""
        self.stats = {
            'total_recipes': 0,
            'total_categories': 0,
            'uncategorized_recipes': 0,
            'categories_created': 0,
            'recipes_updated': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 完善的食谱分类体系
        self.standard_categories = {
            # 主要分类
            '荤菜': {
                'description': '以肉类、禽类、水产为主要食材的菜品',
                'keywords': ['肉', '鸡', '鸭', '鱼', '虾', '蟹', '牛', '猪', '羊', '排骨', '鸡翅', '鸡腿', '牛肉', '猪肉', '羊肉', '鱼片', '鱼块', '虾仁', '蟹肉', '鸡丁', '肉丝', '肉片', '肉丁', '鸡胸', '牛排', '猪排', '羊排', '鸡蛋', '鸭蛋', '鹌鹑蛋']
            },
            '素菜': {
                'description': '以蔬菜为主要食材的菜品',
                'keywords': ['青菜', '白菜', '菠菜', '韭菜', '芹菜', '生菜', '苋菜', '包菜', '花菜', '西兰花', '茄子', '冬瓜', '南瓜', '丝瓜', '苦瓜', '黄瓜', '青椒', '彩椒', '西红柿', '土豆', '山药', '萝卜', '胡萝卜', '红薯', '芋头', '莲藕', '竹笋', '洋葱', '大葱', '蒜', '蒜苔', '生姜']
            },
            '豆制品': {
                'description': '以豆腐、豆干等豆制品为主要食材的菜品',
                'keywords': ['豆腐', '豆干', '腐竹', '豆皮', '千张', '豆腐干', '嫩豆腐', '老豆腐', '香干', '豆腐丝', '豆腐块', '豆腐泡']
            },
            '菌菇类': {
                'description': '以各种菌菇为主要食材的菜品',
                'keywords': ['蘑菇', '香菇', '平菇', '杏鲍菇', '金针菇', '茶树菇', '草菇', '木耳', '银耳', '口蘑', '白灵菇', '猴头菇', '松茸', '牛肝菌']
            },
            '汤品': {
                'description': '各种汤类菜品',
                'keywords': ['汤', '羹', '煲', '炖', '煮', '上汤', '清汤', '浓汤', '鲜汤', '骨汤', '鸡汤', '鱼汤', '蛋花汤', '紫菜汤', '冬瓜汤', '丝瓜汤']
            },
            '主食': {
                'description': '米饭、面条、饼类等主食',
                'keywords': ['饭', '粥', '面', '粉', '饼', '包子', '馒头', '花卷', '烧饼', '油条', '米线', '河粉', '拉面', '刀削面', '炒面', '炒饭', '蒸饭', '煮饭']
            },
            '凉菜': {
                'description': '凉拌菜、腌菜等冷菜',
                'keywords': ['凉拌', '腌', '泡', '拌', '炝拌', '生炝', '凉', '冷', '酸菜', '泡菜', '咸菜', '腌菜']
            },
            '小食': {
                'description': '小点心、零食类',
                'keywords': ['饼', '糕', '点心', '小食', '零食', '酥', '卷', '丸', '球', '条', '片', '块']
            },
            '海鲜': {
                'description': '海鲜水产类菜品',
                'keywords': ['鱼', '虾', '蟹', '贝', '蛤', '蚌', '螺', '鱿鱼', '章鱼', '海参', '海带', '紫菜', '海鲜', '鲍鱼', '扇贝', '生蚝', '龙虾', '基围虾', '河虾', '海虾', '鲜鱿', '干鱿', '鱼片', '鱼块', '鱼丸']
            },
            '蛋类': {
                'description': '以蛋类为主要食材的菜品',
                'keywords': ['蛋', '鸡蛋', '鸭蛋', '鹌鹑蛋', '蛋花', '蛋羹', '蛋饺', '蛋皮', '蛋液', '蛋白', '蛋黄', '煎蛋', '炒蛋', '蒸蛋', '水煮蛋']
            }
        }
        
        # 特殊分类规则
        self.special_rules = {
            # 根据烹饪方法分类
            '炒菜': ['炒', '爆', '煸', '溜'],
            '烧菜': ['烧', '焖', '炖', '煨', '焗'],
            '蒸菜': ['蒸', '蒸蛋', '蒸肉'],
            '煮菜': ['煮', '汆', '涮'],
            '炸菜': ['炸', '酥炸', '油炸'],
            '烤菜': ['烤', '烘', '焙'],
            '凉菜': ['凉拌', '腌', '泡', '醋溜']
        }

    def analyze_current_categories(self) -> Dict:
        """分析当前分类情况"""
        print("📊 分析当前食谱分类情况...")
        
        # 统计总数
        total_recipes = Recipe.query.filter_by(status=1).count()
        total_categories = RecipeCategory.query.count()
        
        self.stats['total_recipes'] = total_recipes
        self.stats['total_categories'] = total_categories
        
        print(f"📋 总食谱数: {total_recipes}")
        print(f"📂 总分类数: {total_categories}")
        
        # 分析分类表中的分类
        print(f"\n🏷️ 分类表中的分类:")
        categories = RecipeCategory.query.all()
        category_stats = {}
        
        for cat in categories:
            recipe_count = Recipe.query.filter_by(category_id=cat.id, status=1).count()
            category_stats[cat.name] = {
                'id': cat.id,
                'count': recipe_count,
                'description': cat.description
            }
            print(f"  {cat.id:2d}. {cat.name:15s} - {recipe_count:3d}个食谱 ({cat.description or '无描述'})")
        
        # 分析食谱category字段分布
        print(f"\n📊 食谱category字段分布:")
        result = db.session.execute(text("""
            SELECT category, COUNT(*) as count 
            FROM recipes 
            WHERE status = 1 
            GROUP BY category 
            ORDER BY count DESC
        """))
        
        category_field_stats = {}
        for row in result:
            category_field_stats[row.category] = row.count
            print(f"  {row.category:15s}: {row.count:3d}个食谱")
        
        # 统计未分类的食谱
        uncategorized = Recipe.query.filter_by(category_id=None, status=1).count()
        self.stats['uncategorized_recipes'] = uncategorized
        print(f"\n⚠️ 未分类食谱: {uncategorized}个")
        
        return {
            'category_table_stats': category_stats,
            'category_field_stats': category_field_stats,
            'uncategorized_count': uncategorized
        }

    def create_standard_categories(self) -> Dict[str, int]:
        """创建标准分类"""
        print(f"\n🏗️ 创建标准分类...")
        
        category_ids = {}
        created_count = 0
        
        for category_name, category_info in self.standard_categories.items():
            # 检查分类是否已存在
            existing_category = RecipeCategory.query.filter_by(name=category_name).first()
            
            if existing_category:
                category_ids[category_name] = existing_category.id
                print(f"  ✅ {category_name} (已存在, ID: {existing_category.id})")
            else:
                # 创建新分类
                new_category = RecipeCategory(
                    name=category_name,
                    description=category_info['description']
                )
                db.session.add(new_category)
                db.session.flush()
                
                category_ids[category_name] = new_category.id
                created_count += 1
                print(f"  ➕ {category_name} (新建, ID: {new_category.id})")
        
        self.stats['categories_created'] = created_count
        print(f"\n✅ 创建了 {created_count} 个新分类")
        
        return category_ids

    def classify_recipe(self, recipe_name: str) -> str:
        """根据食谱名称智能分类"""
        recipe_name_lower = recipe_name.lower()
        
        # 按优先级检查分类
        for category_name, category_info in self.standard_categories.items():
            for keyword in category_info['keywords']:
                if keyword in recipe_name:
                    return category_name
        
        # 如果没有匹配到主要分类，检查特殊规则
        for rule_category, keywords in self.special_rules.items():
            for keyword in keywords:
                if keyword in recipe_name:
                    # 根据特殊规则映射到主要分类
                    if rule_category in ['炒菜', '烧菜', '蒸菜', '煮菜', '炸菜', '烤菜']:
                        return '素菜'  # 默认归为素菜，后续可以根据食材进一步细分
                    elif rule_category == '凉菜':
                        return '凉菜'
        
        # 默认分类
        return '素菜'

    def update_recipe_categories(self, category_ids: Dict[str, int]) -> int:
        """更新食谱分类"""
        print(f"\n🔄 更新食谱分类...")
        
        # 获取所有需要更新分类的食谱
        recipes_to_update = Recipe.query.filter_by(status=1).all()
        
        updated_count = 0
        batch_size = 100
        
        for i, recipe in enumerate(recipes_to_update):
            # 智能分类
            predicted_category = self.classify_recipe(recipe.name)
            predicted_category_id = category_ids.get(predicted_category)
            
            # 检查是否需要更新
            needs_update = False
            
            # 如果没有category_id，需要更新
            if recipe.category_id is None and predicted_category_id:
                recipe.category_id = predicted_category_id
                needs_update = True
            
            # 如果category字段与预测不符，更新category字段
            if recipe.category != predicted_category:
                recipe.category = predicted_category
                needs_update = True
            
            if needs_update:
                updated_count += 1
                if updated_count <= 10:  # 只显示前10个更新示例
                    print(f"  📝 {recipe.name} → {predicted_category}")
            
            # 批量提交
            if (i + 1) % batch_size == 0:
                db.session.commit()
                print(f"    已处理 {i + 1}/{len(recipes_to_update)} 个食谱")
        
        # 最终提交
        db.session.commit()
        
        self.stats['recipes_updated'] = updated_count
        print(f"\n✅ 更新了 {updated_count} 个食谱的分类")
        
        return updated_count

    def generate_category_report(self) -> Dict:
        """生成分类报告"""
        print(f"\n📊 生成分类优化报告...")
        
        # 重新统计分类情况
        final_stats = {}
        
        for category_name in self.standard_categories.keys():
            category = RecipeCategory.query.filter_by(name=category_name).first()
            if category:
                count = Recipe.query.filter_by(category_id=category.id, status=1).count()
                final_stats[category_name] = count
        
        # 统计未分类
        uncategorized = Recipe.query.filter_by(category_id=None, status=1).count()
        final_stats['未分类'] = uncategorized
        
        return final_stats

    def optimize_categories(self) -> Dict:
        """执行分类优化"""
        self.stats['start_time'] = datetime.now()
        
        print("🎯 **食谱分类优化工具**")
        print("=" * 60)
        
        # 1. 分析当前情况
        current_analysis = self.analyze_current_categories()
        
        # 2. 创建标准分类
        category_ids = self.create_standard_categories()
        
        # 3. 更新食谱分类
        self.update_recipe_categories(category_ids)
        
        # 4. 生成最终报告
        final_stats = self.generate_category_report()
        
        self.stats['end_time'] = datetime.now()
        
        # 生成完整报告
        report = {
            'summary': self.stats,
            'current_analysis': current_analysis,
            'final_stats': final_stats,
            'standard_categories': list(self.standard_categories.keys()),
            'timestamp': datetime.now().isoformat()
        }
        
        return report

    def print_optimization_report(self, report: Dict):
        """打印优化报告"""
        print("\n" + "=" * 60)
        print("📊 **食谱分类优化报告**")
        print("=" * 60)
        
        summary = report['summary']
        duration = None
        if summary['start_time'] and summary['end_time']:
            duration = summary['end_time'] - summary['start_time']
        
        print(f"📈 优化统计:")
        print(f"  📋 总食谱数量: {summary['total_recipes']}")
        print(f"  📂 总分类数量: {summary['total_categories']}")
        print(f"  ➕ 新建分类数: {summary['categories_created']}")
        print(f"  🔄 更新食谱数: {summary['recipes_updated']}")
        print(f"  ⚠️ 未分类食谱: {summary['uncategorized_recipes']}")
        
        if duration:
            print(f"  ⏱️ 处理耗时: {duration.total_seconds():.2f} 秒")
        
        print(f"\n📊 **最终分类分布**:")
        final_stats = report['final_stats']
        total_categorized = sum(count for name, count in final_stats.items() if name != '未分类')
        
        for category_name, count in sorted(final_stats.items(), key=lambda x: x[1], reverse=True):
            if category_name != '未分类':
                percentage = (count / total_categorized * 100) if total_categorized > 0 else 0
                print(f"  {category_name:12s}: {count:3d}个食谱 ({percentage:5.1f}%)")
        
        if final_stats.get('未分类', 0) > 0:
            print(f"  {'未分类':12s}: {final_stats['未分类']:3d}个食谱")
        
        print(f"\n🎯 **标准分类体系** ({len(report['standard_categories'])} 个):")
        for i, category in enumerate(report['standard_categories'], 1):
            count = final_stats.get(category, 0)
            print(f"  {i:2d}. {category:12s} - {count:3d}个食谱")
        
        print(f"\n✅ 食谱分类优化完成！")

def main():
    """主函数"""
    print("🎯 **食谱分类优化工具**")
    print("=" * 60)
    print("分析当前分类情况，完善分类体系，优化分类数据")
    print("=" * 60)
    
    # 创建Flask应用上下文
    app = create_app()
    
    with app.app_context():
        # 初始化优化器
        optimizer = RecipeCategoryOptimizer()
        
        try:
            # 执行优化
            report = optimizer.optimize_categories()
            
            # 显示报告
            optimizer.print_optimization_report(report)
            
            # 保存报告
            report_filename = f"recipe_category_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            try:
                with open(report_filename, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2, default=str)
                print(f"\n💾 详细报告已保存到: {report_filename}")
            except Exception as e:
                print(f"⚠️ 保存报告文件失败: {e}")
            
        except Exception as e:
            print(f"❌ 优化过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
