#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极简化食谱分类工具
将所有复杂分类归并到4个基本分类：荤菜、素菜、汤品、主食
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Recipe, RecipeCategory
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class CategorySimplifier:
    def __init__(self):
        """初始化分类简化器"""
        # 4个基本分类
        self.basic_categories = {
            '荤菜': '以肉类、禽类、水产为主的菜品',
            '素菜': '以蔬菜、豆制品、菌菇为主的菜品', 
            '汤品': '各种汤类、羹类菜品',
            '主食': '米饭、面条、粥类等主食'
        }
        
        # 分类映射规则 - 将复杂分类映射到基本分类
        self.category_mapping = {
            # 荤菜类
            '肉类': '荤菜',
            '海鲜类': '荤菜', 
            '海鲜': '荤菜',
            '蛋类': '荤菜',
            
            # 素菜类
            '蔬菜类': '素菜',
            '豆制品': '素菜',
            '菌菇类': '素菜',
            '凉菜': '素菜',
            
            # 汤品类
            '汤类': '汤品',
            
            # 主食类
            '主食类': '主食',
            '小吃': '主食',
            '小吃类': '主食',
            '小食': '主食',
            '水果': '主食',
            '水果类': '主食',
            '饮品类': '主食',
            '其他': '主食'
        }
        
        self.stats = {
            'total_recipes': 0,
            'updated_recipes': 0,
            'categories_created': 0,
            'start_time': None,
            'end_time': None
        }

    def create_basic_categories(self):
        """创建4个基本分类"""
        print("🏗️ 创建基本分类...")
        
        category_ids = {}
        created_count = 0
        
        for name, description in self.basic_categories.items():
            # 检查分类是否已存在
            existing = RecipeCategory.query.filter_by(name=name).first()
            
            if existing:
                category_ids[name] = existing.id
                print(f"  ✅ {name} (已存在, ID: {existing.id})")
            else:
                # 创建新分类
                new_category = RecipeCategory(name=name, description=description)
                db.session.add(new_category)
                db.session.flush()
                
                category_ids[name] = new_category.id
                created_count += 1
                print(f"  ➕ {name} (新建, ID: {new_category.id})")
        
        self.stats['categories_created'] = created_count
        return category_ids

    def classify_recipe(self, recipe_name, current_category):
        """根据食谱名称和当前分类确定基本分类"""
        recipe_name_lower = recipe_name.lower()
        
        # 如果当前分类已经是基本分类之一，直接返回
        if current_category in self.basic_categories:
            return current_category
        
        # 如果当前分类在映射表中，使用映射
        if current_category in self.category_mapping:
            return self.category_mapping[current_category]
        
        # 根据食谱名称关键词判断
        # 荤菜关键词
        meat_keywords = ['肉', '鸡', '鸭', '鱼', '虾', '蟹', '牛', '猪', '羊', '排骨', '鸡翅', '鸡腿', '蛋']
        for keyword in meat_keywords:
            if keyword in recipe_name:
                return '荤菜'
        
        # 汤品关键词
        soup_keywords = ['汤', '羹', '煲']
        for keyword in soup_keywords:
            if keyword in recipe_name:
                return '汤品'
        
        # 主食关键词
        staple_keywords = ['饭', '粥', '面', '粉', '饼', '包子', '馒头']
        for keyword in staple_keywords:
            if keyword in recipe_name:
                return '主食'
        
        # 默认归为素菜
        return '素菜'

    def simplify_all_recipes(self, category_ids):
        """简化所有食谱的分类"""
        print(f"\n🔄 简化食谱分类...")
        
        # 获取所有食谱
        recipes = Recipe.query.filter_by(status=1).all()
        self.stats['total_recipes'] = len(recipes)
        
        updated_count = 0
        batch_size = 50
        
        for i, recipe in enumerate(recipes):
            # 确定新分类
            new_category = self.classify_recipe(recipe.name, recipe.category or '')
            new_category_id = category_ids[new_category]
            
            # 检查是否需要更新
            if recipe.category != new_category or recipe.category_id != new_category_id:
                # 使用原始SQL更新
                sql = text("""
                    UPDATE recipes 
                    SET category = :category, category_id = :category_id 
                    WHERE id = :recipe_id
                """)
                
                db.session.execute(sql, {
                    'category': new_category,
                    'category_id': new_category_id,
                    'recipe_id': recipe.id
                })
                
                updated_count += 1
                
                if updated_count <= 10:  # 只显示前10个更新示例
                    old_category = recipe.category or '无分类'
                    print(f"  📝 {recipe.name} : {old_category} → {new_category}")
            
            # 批量提交
            if (i + 1) % batch_size == 0:
                db.session.commit()
                print(f"    已处理 {i + 1}/{len(recipes)} 个食谱")
        
        # 最终提交
        db.session.commit()
        
        self.stats['updated_recipes'] = updated_count
        print(f"\n✅ 更新了 {updated_count} 个食谱的分类")

    def generate_final_stats(self):
        """生成最终统计"""
        print(f"\n📊 最终分类统计:")
        
        for category_name in self.basic_categories.keys():
            count = db.session.execute(text("""
                SELECT COUNT(*) FROM recipes 
                WHERE category = :category AND status = 1
            """), {'category': category_name}).scalar()
            
            print(f"  {category_name:6s}: {count:3d} 个食谱")

    def simplify(self):
        """执行简化"""
        self.stats['start_time'] = datetime.now()
        
        print("🎯 **食谱分类极简化工具**")
        print("=" * 50)
        print("将所有分类归并到4个基本分类：荤菜、素菜、汤品、主食")
        print("=" * 50)
        
        # 1. 创建基本分类
        category_ids = self.create_basic_categories()
        
        # 2. 简化所有食谱分类
        self.simplify_all_recipes(category_ids)
        
        # 3. 生成最终统计
        self.generate_final_stats()
        
        self.stats['end_time'] = datetime.now()
        duration = self.stats['end_time'] - self.stats['start_time']
        
        print(f"\n✅ 分类简化完成！")
        print(f"📊 处理统计:")
        print(f"  📋 总食谱数: {self.stats['total_recipes']}")
        print(f"  🔄 更新食谱: {self.stats['updated_recipes']}")
        print(f"  ➕ 新建分类: {self.stats['categories_created']}")
        print(f"  ⏱️ 耗时: {duration.total_seconds():.2f} 秒")
        
        print(f"\n🎉 现在食谱分类变得超级简单：")
        print(f"  🥩 荤菜 - 肉类、海鲜、蛋类")
        print(f"  🥬 素菜 - 蔬菜、豆制品、菌菇")
        print(f"  🍲 汤品 - 各种汤羹")
        print(f"  🍚 主食 - 米面粥饼等")

def main():
    """主函数"""
    print("🎯 **食谱分类极简化工具**")
    print("=" * 50)
    
    # 确认操作
    response = input("是否将所有食谱分类简化为4个基本分类？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 创建Flask应用上下文
    app = create_app()
    
    with app.app_context():
        simplifier = CategorySimplifier()
        simplifier.simplify()

if __name__ == "__main__":
    main()
