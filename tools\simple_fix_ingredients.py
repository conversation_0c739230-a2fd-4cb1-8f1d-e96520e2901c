#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单食材修复脚本
直接将所有食材设置为全局，解决显示问题
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app import create_app, db
from sqlalchemy import text

def fix_ingredients():
    """修复食材显示问题"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 检查当前食材情况...")
            
            # 查询总数
            total_result = db.session.execute(text('SELECT COUNT(*) FROM ingredients')).fetchone()
            total_count = total_result[0] if total_result else 0
            print(f"📊 总食材数量: {total_count}")
            
            if total_count == 0:
                print("❌ 没有食材数据")
                return
            
            # 查询需要修复的食材
            need_fix_result = db.session.execute(text('''
                SELECT COUNT(*) FROM ingredients 
                WHERE is_global = 0 OR area_id IS NOT NULL
            ''')).fetchone()
            need_fix_count = need_fix_result[0] if need_fix_result else 0
            
            print(f"🔧 需要修复的食材: {need_fix_count} 个")
            
            if need_fix_count == 0:
                print("✅ 所有食材已经是全局的，无需修复")
                return
            
            # 执行修复
            print("🚀 开始修复...")
            result = db.session.execute(text('''
                UPDATE ingredients 
                SET is_global = 1, area_id = NULL
                WHERE is_global = 0 OR area_id IS NOT NULL
            '''))
            
            affected_rows = result.rowcount
            db.session.commit()
            
            print(f"✅ 成功修复 {affected_rows} 个食材")
            print("🌐 所有食材现在都是全局的，所有学校都可以使用")
            
            # 验证结果
            global_result = db.session.execute(text('SELECT COUNT(*) FROM ingredients WHERE is_global = 1')).fetchone()
            global_count = global_result[0] if global_result else 0
            
            print(f"📊 修复后: {global_count}/{total_count} 个食材为全局")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    print("🍎 **食材显示问题修复工具**")
    print("="*50)
    print("此工具将所有食材设置为全局，解决学校级隔离导致的显示问题")
    print("="*50)
    
    fix_ingredients()
    
    print("\n✅ 修复完成！现在可以访问 http://127.0.0.1:5000/ingredient/ 查看食材")
