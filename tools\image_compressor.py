#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片压缩优化工具
进一步压缩ingredients文件夹中的图片
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageOps
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

class ImageCompressor:
    def __init__(self, target_folder: str = None):
        """
        初始化图片压缩器
        
        Args:
            target_folder: 目标文件夹路径
        """
        if target_folder is None:
            self.target_folder = Path(project_root) / "app" / "static" / "uploads" / "ingredients"
        else:
            self.target_folder = Path(target_folder)
        
        # 压缩参数
        self.compression_levels = {
            'high_quality': {'size': (400, 400), 'quality': 85},      # 当前设置
            'balanced': {'size': (300, 300), 'quality': 75},          # 平衡设置
            'compact': {'size': (250, 250), 'quality': 65},           # 紧凑设置
            'minimal': {'size': (200, 200), 'quality': 55},           # 最小设置
        }
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'original_size': 0,
            'compressed_size': 0,
            'errors': 0
        }
        
        self.errors = []

    def analyze_current_images(self):
        """分析当前图片的状态"""
        print("🔍 **分析当前图片状态**")
        print("="*50)
        
        if not self.target_folder.exists():
            print(f"❌ 文件夹不存在: {self.target_folder}")
            return
        
        files = list(self.target_folder.glob("*.jpg"))
        if not files:
            print("📭 文件夹中没有jpg图片")
            return
        
        print(f"📁 文件夹: {self.target_folder}")
        print(f"📄 图片文件数量: {len(files)}")
        
        # 分析前10个文件
        total_size = 0
        sizes = []
        dimensions = []
        
        print(f"\n📊 前10个文件详细分析:")
        for i, file in enumerate(files[:10], 1):
            try:
                file_size = file.stat().st_size
                sizes.append(file_size)
                
                with Image.open(file) as img:
                    dimensions.append(img.size)
                    print(f"  {i}. {file.name}")
                    print(f"     📏 尺寸: {img.size[0]}x{img.size[1]}")
                    print(f"     📦 大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
                    
            except Exception as e:
                print(f"  {i}. {file.name} - ❌ 分析失败: {e}")
        
        # 计算整体统计
        all_sizes = [f.stat().st_size for f in files]
        total_size = sum(all_sizes)
        avg_size = total_size / len(files)
        max_size = max(all_sizes)
        min_size = min(all_sizes)
        
        print(f"\n📈 整体统计:")
        print(f"  📦 总大小: {total_size:,} 字节 ({total_size/1024/1024:.1f} MB)")
        print(f"  📊 平均大小: {avg_size:,.0f} 字节 ({avg_size/1024:.1f} KB)")
        print(f"  📈 最大文件: {max_size:,} 字节 ({max_size/1024:.1f} KB)")
        print(f"  📉 最小文件: {min_size:,} 字节 ({min_size/1024:.1f} KB)")
        
        if dimensions:
            print(f"  📏 标准尺寸: {dimensions[0]}")
        
        # 压缩潜力分析
        print(f"\n🎯 **压缩潜力分析**:")
        for level_name, params in self.compression_levels.items():
            estimated_size = self.estimate_compressed_size(avg_size, params)
            reduction = ((avg_size - estimated_size) / avg_size) * 100
            total_reduction = ((total_size - estimated_size * len(files)) / total_size) * 100
            
            print(f"  📊 {level_name.upper()}:")
            print(f"     📏 尺寸: {params['size'][0]}x{params['size'][1]}")
            print(f"     🎨 质量: {params['quality']}%")
            print(f"     📦 预估单文件: {estimated_size:.0f} 字节 ({estimated_size/1024:.1f} KB)")
            print(f"     📉 预估减少: {reduction:.1f}%")
            print(f"     💾 总节省空间: {total_reduction:.1f}% ({(total_size - estimated_size * len(files))/1024/1024:.1f} MB)")
            print()

    def estimate_compressed_size(self, current_size: float, params: dict) -> float:
        """估算压缩后的文件大小"""
        # 基于尺寸和质量的简单估算
        size_factor = (params['size'][0] * params['size'][1]) / (400 * 400)  # 相对于当前400x400
        quality_factor = params['quality'] / 85  # 相对于当前85%质量
        
        return current_size * size_factor * quality_factor * 0.8  # 0.8是经验系数

    def compress_single_image(self, source_path: Path, target_size: tuple, quality: int) -> bool:
        """压缩单个图片"""
        try:
            # 创建备份文件名
            backup_path = source_path.with_suffix('.backup')
            
            # 备份原文件
            shutil.copy2(source_path, backup_path)
            
            # 压缩图片
            with Image.open(source_path) as img:
                # 转换为RGB模式
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 调整大小
                img = ImageOps.fit(img, target_size, Image.Resampling.LANCZOS)
                
                # 保存压缩后的图片
                img.save(source_path, 'JPEG', quality=quality, optimize=True)
            
            # 删除备份文件
            backup_path.unlink()
            
            return True
            
        except Exception as e:
            # 如果压缩失败，恢复备份
            if backup_path.exists():
                shutil.copy2(backup_path, source_path)
                backup_path.unlink()
            
            self.errors.append(f"压缩失败 {source_path.name}: {e}")
            return False

    def batch_compress(self, compression_level: str = 'balanced', confirm: bool = True):
        """批量压缩图片"""
        if compression_level not in self.compression_levels:
            print(f"❌ 无效的压缩级别: {compression_level}")
            return
        
        params = self.compression_levels[compression_level]
        
        print(f"\n🚀 **批量压缩图片 - {compression_level.upper()}级别**")
        print("="*60)
        print(f"📏 目标尺寸: {params['size'][0]}x{params['size'][1]}")
        print(f"🎨 压缩质量: {params['quality']}%")
        
        files = list(self.target_folder.glob("*.jpg"))
        if not files:
            print("📭 没有找到jpg图片文件")
            return
        
        print(f"📄 待处理文件: {len(files)} 个")
        
        if confirm:
            response = input(f"\n⚠️ 即将压缩 {len(files)} 个图片文件，此操作不可逆！\n是否继续？(y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return
        
        # 记录开始时间和原始大小
        start_time = datetime.now()
        original_total_size = sum(f.stat().st_size for f in files)
        self.stats['original_size'] = original_total_size
        self.stats['total_files'] = len(files)
        
        print(f"\n📊 压缩前总大小: {original_total_size:,} 字节 ({original_total_size/1024/1024:.1f} MB)")
        print(f"🚀 开始压缩...")
        
        # 批量处理
        for i, file in enumerate(files, 1):
            print(f"[{i}/{len(files)}] 处理: {file.name}", end=" ... ")
            
            original_size = file.stat().st_size
            
            if self.compress_single_image(file, params['size'], params['quality']):
                new_size = file.stat().st_size
                reduction = ((original_size - new_size) / original_size) * 100
                print(f"✅ {original_size:,} → {new_size:,} 字节 (-{reduction:.1f}%)")
                self.stats['processed_files'] += 1
            else:
                print("❌ 失败")
                self.stats['errors'] += 1
        
        # 计算最终统计
        end_time = datetime.now()
        final_total_size = sum(f.stat().st_size for f in files)
        self.stats['compressed_size'] = final_total_size
        
        total_reduction = ((original_total_size - final_total_size) / original_total_size) * 100
        space_saved = original_total_size - final_total_size
        
        print(f"\n📊 **压缩完成报告**")
        print("="*50)
        print(f"⏱️ 处理时间: {(end_time - start_time).total_seconds():.1f} 秒")
        print(f"📄 处理文件: {self.stats['processed_files']}/{self.stats['total_files']}")
        print(f"❌ 错误数量: {self.stats['errors']}")
        print(f"📦 压缩前: {original_total_size:,} 字节 ({original_total_size/1024/1024:.1f} MB)")
        print(f"📦 压缩后: {final_total_size:,} 字节 ({final_total_size/1024/1024:.1f} MB)")
        print(f"💾 节省空间: {space_saved:,} 字节 ({space_saved/1024/1024:.1f} MB)")
        print(f"📈 压缩率: {total_reduction:.1f}%")
        
        if self.errors:
            print(f"\n❌ **错误详情** ({len(self.errors)} 个):")
            for error in self.errors[:10]:
                print(f"  • {error}")
            if len(self.errors) > 10:
                print(f"  ... 还有 {len(self.errors) - 10} 个错误")

def main():
    """主函数"""
    print("🖼️ **图片压缩优化工具**")
    print("="*60)
    
    compressor = ImageCompressor()
    
    # 分析当前状态
    compressor.analyze_current_images()
    
    # 询问是否进行压缩
    print(f"\n💡 **建议**:")
    print("• BALANCED: 平衡质量和大小，适合大多数情况")
    print("• COMPACT: 更小的文件，质量仍然可接受")
    print("• MINIMAL: 最小文件大小，质量有所降低")
    
    choice = input(f"\n选择压缩级别 (balanced/compact/minimal) 或按Enter跳过: ").lower().strip()
    
    if choice in ['balanced', 'compact', 'minimal']:
        compressor.batch_compress(choice)
    elif choice == '':
        print("⏭️ 跳过压缩")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
