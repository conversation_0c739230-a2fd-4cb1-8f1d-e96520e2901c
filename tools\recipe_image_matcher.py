#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食谱图片智能匹配工具
读取D:\XIANGCAI文件夹中的JPG图片，智能匹配并补充系统食谱图片
"""

import os
import sys
import uuid
import re
from pathlib import Path
from PIL import Image, ImageOps
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json
from difflib import SequenceMatcher

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Recipe
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class RecipeImageMatcher:
    def __init__(self, source_folder: str = "D:/xiangchai", target_folder: str = None):
        """
        初始化食谱图片匹配器

        Args:
            source_folder: 源图片文件夹路径
            target_folder: 目标图片文件夹路径
        """
        self.source_folder = Path(source_folder)

        if target_folder is None:
            self.target_folder = Path(project_root) / "app" / "static" / "uploads" / "recipes"
        else:
            self.target_folder = Path(target_folder)

        # 图片处理参数
        self.target_size = (400, 400)
        self.quality = 85

        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.JPG', '.JPEG'}

        # 统计信息
        self.stats = {
            'total_images': 0,
            'total_recipes': 0,
            'matched_recipes': 0,
            'updated_images': 0,
            'added_images': 0,
            'unmatched_images': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }

        self.errors = []
        self.matched_pairs = []
        self.unmatched_images = []

        # 食谱名称清理和标准化规则
        self.name_cleaning_patterns = [
            # 去除数字编号
            (r'^\d+\.?\s*', ''),
            (r'^\d+[-_]\s*', ''),

            # 去除括号内容
            (r'\([^)]*\)', ''),
            (r'\[[^\]]*\]', ''),
            (r'（[^）]*）', ''),

            # 去除常见后缀
            (r'[_\-\s]*副本\d*$', ''),
            (r'[_\-\s]*copy\d*$', ''),
            (r'[_\-\s]*备份\d*$', ''),
            (r'[_\-\s]*\d+$', ''),

            # 标准化分隔符
            (r'[_\-]+', ''),
            (r'\s+', ''),
        ]

        # 食谱名称映射表（处理不同的命名方式）
        self.recipe_mapping = {
            # 主食类
            '米饭': ['米饭', '白米饭', '大米饭', '蒸米饭'],
            '玉米饭': ['玉米饭', '玉米米饭', '二米饭'],
            '二米饭': ['二米饭', '玉米饭', '小米饭'],
            '红薯米饭': ['红薯米饭', '红薯饭', '地瓜饭'],

            # 荤菜类
            '香菇炖鸡': ['香菇炖鸡', '香菇鸡', '鸡炖香菇', '香菇烧鸡'],
            '香干炒肉': ['香干炒肉', '豆干炒肉', '香干肉丝'],
            '胡萝卜烧鸭子': ['胡萝卜烧鸭', '胡萝卜鸭', '烧鸭胡萝卜'],
            '青椒炒鸡': ['青椒炒鸡', '辣椒炒鸡', '青椒鸡丁'],
            '黄豆红烧肉': ['黄豆红烧肉', '红烧肉黄豆', '黄豆烧肉'],
            '蒜台炒牛肉': ['蒜台炒牛肉', '蒜苔牛肉', '牛肉炒蒜苔'],
            '黄瓜红椒炒肉': ['黄瓜红椒炒肉', '黄瓜炒肉', '黄瓜肉丝'],
            '黄瓜炒猪肝': ['黄瓜炒猪肝', '猪肝黄瓜', '黄瓜猪肝'],
            '杏鲍菇炒肉': ['杏鲍菇炒肉', '杏鲍菇肉片', '炒杏鲍菇'],
            '香辣基围虾': ['香辣基围虾', '基围虾', '辣炒虾'],
            '花菜炒肉': ['花菜炒肉', '菜花炒肉', '花椰菜炒肉'],
            '芹菜炒肉': ['芹菜炒肉', '芹菜肉丝', '炒芹菜'],

            # 素菜类
            '韭菜炒豆芽': ['韭菜炒豆芽', '豆芽韭菜', '韭菜豆芽'],
            '香菇炒上海青': ['香菇炒上海青', '香菇青菜', '上海青香菇'],
            '清炒红薯叶': ['清炒红薯叶', '红薯叶', '地瓜叶'],
            '清炒红苋菜': ['清炒红苋菜', '红苋菜', '苋菜'],
            '清炒生菜': ['清炒生菜', '生菜', '炒生菜'],
            '炒大白菜': ['炒大白菜', '大白菜', '白菜'],
            '洋葱炒蛋': ['洋葱炒蛋', '洋葱鸡蛋', '炒洋葱'],
            '炒包菜': ['炒包菜', '包菜', '卷心菜', '圆白菜'],
            '西红柿炒蛋': ['西红柿炒蛋', '番茄炒蛋', '炒鸡蛋'],
            '炒豇豆': ['炒豇豆', '豇豆', '长豆角'],
        }

    def normalize_name(self, name: str) -> str:
        """
        标准化名称（去除数字、符号等）

        Args:
            name: 原始名称

        Returns:
            str: 标准化后的名称
        """
        if not name:
            return ''

        normalized = name.strip()

        # 应用清理规则
        for pattern, replacement in self.name_cleaning_patterns:
            normalized = re.sub(pattern, replacement, normalized, flags=re.IGNORECASE)

        return normalized.strip()

    def calculate_similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度

        Args:
            str1: 字符串1
            str2: 字符串2

        Returns:
            float: 相似度 (0-1)
        """
        return SequenceMatcher(None, str1, str2).ratio()

    def find_best_recipe_match(self, image_name: str, recipes: List[Recipe]) -> Optional[Tuple[Recipe, float]]:
        """
        为图片找到最佳匹配的食谱

        Args:
            image_name: 图片文件名（不含扩展名）
            recipes: 食谱列表

        Returns:
            Optional[Tuple[Recipe, float]]: 最佳匹配的食谱和相似度
        """
        normalized_image_name = self.normalize_name(image_name)
        best_match = None
        best_score = 0.0

        for recipe in recipes:
            recipe_name = recipe.name
            normalized_recipe_name = self.normalize_name(recipe_name)

            # 1. 精确匹配
            if normalized_image_name == normalized_recipe_name:
                return recipe, 1.0

            # 2. 检查映射表
            if recipe_name in self.recipe_mapping:
                for variant in self.recipe_mapping[recipe_name]:
                    normalized_variant = self.normalize_name(variant)
                    if normalized_image_name == normalized_variant:
                        return recipe, 0.95

                    # 包含匹配
                    if normalized_image_name in normalized_variant or normalized_variant in normalized_image_name:
                        score = 0.9
                        if score > best_score:
                            best_match = recipe
                            best_score = score

            # 3. 直接相似度匹配
            similarity = self.calculate_similarity(normalized_image_name, normalized_recipe_name)
            if similarity > best_score and similarity >= 0.6:  # 设置最低相似度阈值
                best_match = recipe
                best_score = similarity

            # 4. 包含匹配
            if normalized_image_name in normalized_recipe_name or normalized_recipe_name in normalized_image_name:
                score = 0.7
                if score > best_score:
                    best_match = recipe
                    best_score = score

        return (best_match, best_score) if best_match and best_score >= 0.6 else None

    def compress_and_save_image(self, source_path: Path, target_path: Path) -> bool:
        """
        压缩并保存图片

        Args:
            source_path: 源图片路径
            target_path: 目标图片路径

        Returns:
            bool: 是否成功
        """
        try:
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)

            with Image.open(source_path) as img:
                # 转换为RGB模式（处理RGBA、P等模式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # 使用高质量重采样算法调整大小
                img = ImageOps.fit(img, self.target_size, Image.Resampling.LANCZOS)

                # 保存压缩后的图片
                img.save(target_path, 'JPEG', quality=self.quality, optimize=True)

                return True

        except Exception as e:
            self.errors.append(f"图片压缩失败 {source_path}: {e}")
            return False

    def update_recipe_image(self, recipe: Recipe, image_filename: str) -> bool:
        """
        更新食谱图片

        Args:
            recipe: 食谱对象
            image_filename: 图片文件名

        Returns:
            bool: 是否成功
        """
        try:
            image_path = f"uploads/recipes/{image_filename}"

            # 使用原始SQL更新图片路径
            sql = text("""
                UPDATE recipes
                SET main_image = :main_image,
                    updated_at = GETDATE()
                WHERE id = :id
            """)

            db.session.execute(sql, {
                'main_image': image_path,
                'id': recipe.id
            })

            if recipe.main_image:
                self.stats['updated_images'] += 1
                action = "更新"
            else:
                self.stats['added_images'] += 1
                action = "添加"

            print(f"✅ {action}图片: {recipe.name} [ID: {recipe.id}]")
            return True

        except Exception as e:
            error_msg = f"更新食谱图片失败 {recipe.name}: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
            return False

    def scan_images_and_recipes(self) -> Tuple[List[Path], List[Recipe]]:
        """
        扫描图片文件和食谱

        Returns:
            Tuple[List[Path], List[Recipe]]: 图片文件列表和食谱列表
        """
        print(f"📂 扫描源文件夹: {self.source_folder}")

        if not self.source_folder.exists():
            raise FileNotFoundError(f"源文件夹不存在: {self.source_folder}")

        # 获取所有JPG文件
        image_files = []
        for ext in self.image_extensions:
            image_files.extend(self.source_folder.glob(f"*{ext}"))

        self.stats['total_images'] = len(image_files)
        print(f"🖼️ 找到图片文件: {len(image_files)} 个")

        # 获取所有食谱
        recipes = Recipe.query.filter_by(status=1).all()  # 只获取启用的食谱
        self.stats['total_recipes'] = len(recipes)
        print(f"📋 系统食谱数量: {len(recipes)} 个")

        return image_files, recipes

    def batch_match_and_update(self, image_files: List[Path], recipes: List[Recipe]) -> Dict:
        """
        批量匹配并更新食谱图片

        Args:
            image_files: 图片文件列表
            recipes: 食谱列表

        Returns:
            Dict: 处理报告
        """
        self.stats['start_time'] = datetime.now()

        print(f"🚀 开始智能匹配分析...")
        print(f"📊 待处理: {len(image_files)} 个图片文件")

        # 第一阶段：为每个食谱收集所有可能的匹配图片
        recipe_matches = {}  # {recipe_id: [(image_file, similarity, image_name), ...]}

        for i, image_file in enumerate(image_files, 1):
            progress = (i / len(image_files)) * 100
            print(f"[{progress:.1f}%] 分析: {image_file.name}")

            # 获取文件名（不含扩展名）
            image_name = image_file.stem

            # 查找最佳匹配的食谱
            match_result = self.find_best_recipe_match(image_name, recipes)

            if match_result:
                recipe, similarity = match_result

                if recipe.id not in recipe_matches:
                    recipe_matches[recipe.id] = []

                recipe_matches[recipe.id].append((image_file, similarity, image_name))
                print(f"  📝 候选匹配: {recipe.name} (相似度: {similarity:.2f})")
            else:
                self.unmatched_images.append(image_name)
                self.stats['unmatched_images'] += 1
                print(f"  ⚠️ 未找到匹配: {image_name}")

        print(f"\n🎯 第二阶段：为每个食谱选择最佳图片...")
        print(f"📊 找到 {len(recipe_matches)} 个食谱的候选图片")

        # 第二阶段：为每个食谱选择相似度最高的图片
        recipe_id_to_recipe = {recipe.id: recipe for recipe in recipes}

        for recipe_id, matches in recipe_matches.items():
            recipe = recipe_id_to_recipe[recipe_id]

            # 按相似度排序，选择最高的
            best_match = max(matches, key=lambda x: x[1])
            image_file, similarity, image_name = best_match

            print(f"🏆 {recipe.name}: 选择最佳匹配 '{image_name}' (相似度: {similarity:.2f})")
            if len(matches) > 1:
                print(f"   📊 从 {len(matches)} 个候选中选出，其他候选相似度: {[f'{m[1]:.2f}' for m in matches if m != best_match]}")

            # 处理图片
            image_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}.jpg"
            target_image_path = self.target_folder / image_filename

            if self.compress_and_save_image(image_file, target_image_path):
                if self.update_recipe_image(recipe, image_filename):
                    self.stats['matched_recipes'] += 1
                    self.matched_pairs.append({
                        'image_name': image_name,
                        'recipe_name': recipe.name,
                        'recipe_id': recipe.id,
                        'similarity': similarity,
                        'image_file': image_filename,
                        'total_candidates': len(matches)
                    })
                    print(f"  ✅ 更新成功: {recipe.name}")
                else:
                    self.stats['errors'] += 1
            else:
                self.stats['errors'] += 1

        # 提交所有更改
        try:
            db.session.commit()
            print(f"\n✅ 所有更改已提交到数据库")
        except Exception as e:
            db.session.rollback()
            error_msg = f"数据库提交失败: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")

        self.stats['end_time'] = datetime.now()

        # 生成报告
        return self.generate_report()

    def generate_report(self) -> Dict:
        """生成处理报告"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']

        report = {
            'summary': {
                'total_images_scanned': self.stats['total_images'],
                'total_recipes_in_system': self.stats['total_recipes'],
                'recipes_matched': self.stats['matched_recipes'],
                'images_updated': self.stats['updated_images'],
                'images_added': self.stats['added_images'],
                'images_unmatched': self.stats['unmatched_images'],
                'errors_occurred': self.stats['errors'],
                'duration_seconds': duration.total_seconds() if duration else None,
                'match_rate': (self.stats['matched_recipes'] / max(self.stats['total_images'], 1)) * 100
            },
            'matched_pairs': self.matched_pairs,
            'unmatched_images': self.unmatched_images,
            'errors': self.errors,
            'timestamp': datetime.now().isoformat()
        }

        # 保存报告到文件
        report_filename = f"recipe_image_match_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            report['report_file'] = report_filename
        except Exception as e:
            print(f"⚠️ 保存报告文件失败: {e}")

        return report

    def print_match_report(self, report: Dict):
        """打印匹配报告"""
        print("\n" + "="*60)
        print("🖼️ **食谱图片智能匹配报告**")
        print("="*60)

        summary = report['summary']

        print(f"📊 匹配统计:")
        print(f"  📂 扫描图片总数: {summary['total_images_scanned']}")
        print(f"  📋 系统食谱总数: {summary['total_recipes_in_system']}")
        print(f"  ✅ 成功匹配食谱: {summary['recipes_matched']}")
        print(f"  🔄 更新图片数量: {summary['images_updated']}")
        print(f"  📷 新增图片数量: {summary['images_added']}")
        print(f"  ❌ 未匹配图片数: {summary['images_unmatched']}")
        print(f"  ❌ 处理错误数量: {summary['errors_occurred']}")
        print(f"  📈 匹配成功率: {summary['match_rate']:.1f}%")

        if summary['duration_seconds']:
            print(f"  ⏱️ 总耗时: {summary['duration_seconds']:.2f} 秒")

        # 显示匹配成功的案例
        if report['matched_pairs']:
            print(f"\n📋 **匹配成功案例** ({len(report['matched_pairs'])} 个):")
            for i, pair in enumerate(report['matched_pairs'][:10], 1):
                candidates_info = ""
                if 'total_candidates' in pair and pair['total_candidates'] > 1:
                    candidates_info = f" [从{pair['total_candidates']}个候选中选出]"
                print(f"  {i}. {pair['image_name']} → {pair['recipe_name']} (相似度: {pair['similarity']:.2f}){candidates_info}")
            if len(report['matched_pairs']) > 10:
                print(f"  ... 还有 {len(report['matched_pairs']) - 10} 个匹配")

        # 显示未匹配的图片
        if report['unmatched_images']:
            print(f"\n⚠️ **未匹配的图片** ({len(report['unmatched_images'])} 个):")
            for i, image_name in enumerate(report['unmatched_images'][:10], 1):
                print(f"  {i}. {image_name}")
            if len(report['unmatched_images']) > 10:
                print(f"  ... 还有 {len(report['unmatched_images']) - 10} 个")

        if report['errors']:
            print(f"\n❌ **错误详情** ({len(report['errors'])} 个):")
            for i, error in enumerate(report['errors'][:5], 1):
                print(f"  {i}. {error}")
            if len(report['errors']) > 5:
                print(f"  ... 还有 {len(report['errors']) - 5} 个错误")

        if 'report_file' in report:
            print(f"\n💾 详细报告已保存到: {report['report_file']}")

        print("\n✅ 食谱图片智能匹配完成！")

def main():
    """主函数"""
    print("🖼️ **食谱图片智能匹配工具**")
    print("="*60)
    print("读取D:\\xiangchai文件夹中的JPG图片，智能匹配并补充系统食谱图片")
    print("="*60)

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 初始化匹配器
        matcher = RecipeImageMatcher()

        try:
            # 检查默认路径，如果不存在则询问用户
            if not matcher.source_folder.exists():
                print(f"❌ 默认路径不存在: {matcher.source_folder}")
                custom_path = input("请输入图片文件夹的完整路径 (例如: D:/xiangchai): ")
                if custom_path:
                    matcher.source_folder = Path(custom_path)
                    if not matcher.source_folder.exists():
                        print(f"❌ 指定路径也不存在: {matcher.source_folder}")
                        return
                else:
                    print("❌ 未指定有效路径，退出程序")
                    return

            # 扫描图片和食谱
            image_files, recipes = matcher.scan_images_and_recipes()

            if not image_files:
                print("❌ 没有找到JPG图片文件")
                return

            if not recipes:
                print("❌ 系统中没有食谱")
                return

            # 显示扫描结果
            print(f"\n📊 扫描结果:")
            print(f"  🖼️ 图片文件: {len(image_files)} 个")
            print(f"  📋 系统食谱: {len(recipes)} 个")

            # 显示一些图片文件示例
            print(f"\n📋 图片文件示例 (前10个):")
            for i, image_file in enumerate(image_files[:10], 1):
                file_size = image_file.stat().st_size
                print(f"  {i}. {image_file.name} ({file_size:,} 字节)")

            if len(image_files) > 10:
                print(f"  ... 还有 {len(image_files) - 10} 个图片文件")

            # 询问是否继续
            response = input(f"\n是否开始智能匹配这 {len(image_files)} 个图片？(y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return

            # 执行批量匹配和更新
            report = matcher.batch_match_and_update(image_files, recipes)

            # 显示报告
            matcher.print_match_report(report)

            # 显示最终统计
            if report['summary']['recipes_matched'] > 0:
                print(f"\n🎉 食谱图片匹配成功！")
                print(f"📊 匹配成果:")
                print(f"  ✅ 成功匹配: {report['summary']['recipes_matched']} 个食谱")
                print(f"  📷 新增图片: {report['summary']['images_added']} 个")
                print(f"  🔄 更新图片: {report['summary']['images_updated']} 个")
                print(f"  📈 匹配率: {report['summary']['match_rate']:.1f}%")

                print(f"\n🌐 您可以访问 http://127.0.0.1:5000/recipe/ 查看更新后的食谱图片")

        except Exception as e:
            print(f"❌ 处理过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()