#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库食材去重工具
分析并去除数据库中的重复食材
"""

import os
import sys
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Set
import json
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory
    from sqlalchemy import text, func
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class IngredientDeduplicator:
    def __init__(self):
        """初始化食材去重器"""
        self.stats = {
            'total_ingredients': 0,
            'duplicate_groups': 0,
            'total_duplicates': 0,
            'ingredients_to_delete': 0,
            'ingredients_to_keep': 0,
            'categories_analyzed': 0,
            'start_time': None,
            'end_time': None
        }

        self.duplicate_groups = []
        self.ingredients_to_delete = []
        self.ingredients_to_keep = []

        # 常见的食材名称变体模式
        self.normalization_patterns = [
            # 去除括号内容
            (r'\([^)]*\)', ''),
            (r'\[[^\]]*\]', ''),
            (r'（[^）]*）', ''),

            # 去除数字和编号
            (r'^\d+\.?\s*', ''),  # 开头的数字编号
            (r'\s*\d+$', ''),     # 结尾的数字
            (r'[_\-\s]*\d+$', ''),  # 末尾数字

            # 去除常见后缀
            (r'[_\-\s]*副本\d*$', ''),
            (r'[_\-\s]*copy\d*$', ''),
            (r'[_\-\s]*备份\d*$', ''),

            # 标准化空格和分隔符
            (r'[_\-]+', ' '),
            (r'\s+', ' '),
        ]

    def normalize_ingredient_name(self, name: str) -> str:
        """
        标准化食材名称

        Args:
            name: 原始食材名称

        Returns:
            str: 标准化后的名称
        """
        if not name:
            return ''

        normalized = name.strip()

        # 应用标准化模式
        for pattern, replacement in self.normalization_patterns:
            normalized = re.sub(pattern, replacement, normalized, flags=re.IGNORECASE)

        # 去除首尾空白并转换为小写进行比较
        normalized = normalized.strip().lower()

        return normalized

    def analyze_database(self) -> Dict:
        """分析数据库中的食材重复情况"""
        print("🔍 分析数据库中的食材...")

        self.stats['start_time'] = datetime.now()

        # 获取所有食材
        ingredients = Ingredient.query.all()
        self.stats['total_ingredients'] = len(ingredients)

        print(f"📊 数据库中共有 {len(ingredients)} 个食材")

        # 按分类分组分析
        categories = {}
        for ingredient in ingredients:
            category = ingredient.category or '未分类'
            if category not in categories:
                categories[category] = []
            categories[category].append(ingredient)

        self.stats['categories_analyzed'] = len(categories)

        print(f"📋 涉及 {len(categories)} 个分类:")
        for category, items in categories.items():
            print(f"  - {category}: {len(items)} 个食材")

        # 分析每个分类中的重复
        all_duplicates = {}

        for category, items in categories.items():
            print(f"\n🔍 分析分类: {category}")
            duplicates = self.find_duplicates_in_category(items)
            if duplicates:
                all_duplicates[category] = duplicates
                print(f"  发现 {len(duplicates)} 组重复食材")

        return {
            'categories': categories,
            'duplicates': all_duplicates,
            'total_ingredients': len(ingredients)
        }

    def find_duplicates_in_category(self, ingredients: List[Ingredient]) -> List[List[Ingredient]]:
        """
        在单个分类中查找重复的食材

        Args:
            ingredients: 该分类下的所有食材

        Returns:
            List[List[Ingredient]]: 重复食材组列表
        """
        # 按标准化名称分组
        name_groups = defaultdict(list)

        for ingredient in ingredients:
            normalized_name = self.normalize_ingredient_name(ingredient.name)
            if normalized_name:  # 忽略空名称
                name_groups[normalized_name].append(ingredient)

        # 找出有重复的组
        duplicate_groups = []
        for normalized_name, group in name_groups.items():
            if len(group) > 1:
                # 按创建时间排序，保留最早创建的
                def get_sort_key(x):
                    if x.created_at:
                        if isinstance(x.created_at, str):
                            try:
                                return datetime.fromisoformat(x.created_at.replace('Z', '+00:00'))
                            except:
                                return datetime.min
                        else:
                            return x.created_at
                    return datetime.min

                group.sort(key=get_sort_key)
                duplicate_groups.append(group)

                print(f"    🔄 发现重复: '{normalized_name}' ({len(group)} 个)")
                for i, ing in enumerate(group):
                    status = "保留" if i == 0 else "删除"
                    # 处理created_at可能是字符串的情况
                    if ing.created_at:
                        if isinstance(ing.created_at, str):
                            created = ing.created_at
                        else:
                            created = ing.created_at.strftime('%Y-%m-%d %H:%M')
                    else:
                        created = '未知'
                    print(f"      {i+1}. [{status}] {ing.name} (ID: {ing.id}, 创建: {created})")

        return duplicate_groups

    def generate_deduplication_plan(self, analysis_result: Dict) -> Dict:
        """
        生成去重计划

        Args:
            analysis_result: 分析结果

        Returns:
            Dict: 去重计划
        """
        print("\n📋 生成去重计划...")

        plan = {
            'to_keep': [],
            'to_delete': [],
            'summary': {
                'total_groups': 0,
                'total_to_delete': 0,
                'total_to_keep': 0
            }
        }

        for category, duplicate_groups in analysis_result['duplicates'].items():
            for group in duplicate_groups:
                plan['summary']['total_groups'] += 1

                # 保留第一个（最早创建的）
                to_keep = group[0]
                to_delete = group[1:]

                # 处理时间格式
                def format_time(dt):
                    if dt:
                        if isinstance(dt, str):
                            return dt
                        else:
                            return dt.strftime('%Y-%m-%d %H:%M')
                    return '未知'

                plan['to_keep'].append({
                    'id': to_keep.id,
                    'name': to_keep.name,
                    'category': category,
                    'created_at': format_time(to_keep.created_at),
                    'has_image': bool(to_keep.base_image),
                    'group_size': len(group)
                })

                for item in to_delete:
                    plan['to_delete'].append({
                        'id': item.id,
                        'name': item.name,
                        'category': category,
                        'created_at': format_time(item.created_at),
                        'has_image': bool(item.base_image),
                        'keep_id': to_keep.id,
                        'keep_name': to_keep.name
                    })

                plan['summary']['total_to_keep'] += 1
                plan['summary']['total_to_delete'] += len(to_delete)

        self.stats['duplicate_groups'] = plan['summary']['total_groups']
        self.stats['ingredients_to_delete'] = plan['summary']['total_to_delete']
        self.stats['ingredients_to_keep'] = plan['summary']['total_to_keep']
        self.stats['total_duplicates'] = plan['summary']['total_to_delete']

        return plan

    def print_deduplication_plan(self, plan: Dict):
        """打印去重计划"""
        print("\n" + "="*60)
        print("📋 **食材去重计划**")
        print("="*60)

        summary = plan['summary']
        print(f"🔍 发现重复组: {summary['total_groups']} 组")
        print(f"✅ 保留食材: {summary['total_to_keep']} 个")
        print(f"🗑️ 删除食材: {summary['total_to_delete']} 个")

        if summary['total_groups'] > 0:
            print(f"\n📊 **详细计划**:")

            # 按分类分组显示
            by_category = defaultdict(list)
            for item in plan['to_delete']:
                by_category[item['category']].append(item)

            for category, items in by_category.items():
                print(f"\n📂 {category} ({len(items)} 个待删除):")

                # 按保留的食材分组
                by_keep = defaultdict(list)
                for item in items:
                    by_keep[item['keep_name']].append(item)

                for keep_name, delete_items in by_keep.items():
                    print(f"  ✅ 保留: {keep_name}")
                    for item in delete_items:
                        created = item['created_at'] if item['created_at'] else '未知'
                        image_status = "有图片" if item['has_image'] else "无图片"
                        print(f"    🗑️ 删除: {item['name']} (ID: {item['id']}, {created}, {image_status})")

    def execute_deduplication(self, plan: Dict, dry_run: bool = True) -> Dict:
        """
        执行去重操作

        Args:
            plan: 去重计划
            dry_run: 是否为试运行

        Returns:
            Dict: 执行结果
        """
        if dry_run:
            print("\n🔍 **试运行模式** - 不会实际删除数据")
        else:
            print("\n🚀 **执行去重操作**")

        result = {
            'deleted_count': 0,
            'errors': [],
            'deleted_items': []
        }

        if not dry_run:
            try:
                for item in plan['to_delete']:
                    # 删除食材
                    ingredient = Ingredient.query.get(item['id'])
                    if ingredient:
                        db.session.delete(ingredient)
                        result['deleted_items'].append(item)
                        result['deleted_count'] += 1
                        print(f"🗑️ 删除: {item['name']} (ID: {item['id']})")

                # 提交更改
                db.session.commit()
                print(f"\n✅ 成功删除 {result['deleted_count']} 个重复食材")

            except Exception as e:
                db.session.rollback()
                error_msg = f"删除操作失败: {e}"
                result['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        else:
            result['deleted_count'] = len(plan['to_delete'])
            print(f"🔍 试运行完成，将删除 {result['deleted_count']} 个重复食材")

        return result

    def generate_report(self, analysis_result: Dict, plan: Dict, execution_result: Dict) -> Dict:
        """生成去重报告"""
        self.stats['end_time'] = datetime.now()
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']

        report = {
            'summary': {
                'total_ingredients_analyzed': self.stats['total_ingredients'],
                'categories_analyzed': self.stats['categories_analyzed'],
                'duplicate_groups_found': self.stats['duplicate_groups'],
                'total_duplicates_found': self.stats['total_duplicates'],
                'ingredients_deleted': execution_result['deleted_count'],
                'ingredients_remaining': self.stats['total_ingredients'] - execution_result['deleted_count'],
                'errors_occurred': len(execution_result['errors']),
                'duration_seconds': duration.total_seconds() if duration else None
            },
            'categories': {cat: len(items) for cat, items in analysis_result['categories'].items()},
            'duplicate_groups': plan,
            'execution_result': execution_result,
            'errors': execution_result['errors'],
            'timestamp': datetime.now().isoformat()
        }

        # 保存报告到文件
        report_filename = f"ingredient_deduplication_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            report['report_file'] = report_filename
        except Exception as e:
            print(f"⚠️ 保存报告文件失败: {e}")

        return report

    def print_final_report(self, report: Dict):
        """打印最终报告"""
        print("\n" + "="*60)
        print("📊 **食材去重最终报告**")
        print("="*60)

        summary = report['summary']

        print(f"📊 分析统计:")
        print(f"  📄 总食材数量: {summary['total_ingredients_analyzed']}")
        print(f"  📂 分析分类数: {summary['categories_analyzed']}")
        print(f"  🔄 发现重复组: {summary['duplicate_groups_found']}")
        print(f"  🗑️ 重复食材数: {summary['total_duplicates_found']}")

        print(f"\n🎯 执行结果:")
        print(f"  ✅ 删除食材数: {summary['ingredients_deleted']}")
        print(f"  📦 剩余食材数: {summary['ingredients_remaining']}")
        print(f"  ❌ 错误数量: {summary['errors_occurred']}")

        if summary['duration_seconds']:
            print(f"  ⏱️ 总耗时: {summary['duration_seconds']:.2f} 秒")

        # 显示分类统计
        print(f"\n📂 分类统计:")
        for category, count in report['categories'].items():
            print(f"  - {category}: {count} 个食材")

        if report['errors']:
            print(f"\n❌ 错误详情:")
            for error in report['errors']:
                print(f"  - {error}")

        if 'report_file' in report:
            print(f"\n💾 详细报告已保存到: {report['report_file']}")

        print("\n✅ 食材去重完成！")

def main():
    """主函数"""
    print("🔍 **数据库食材去重工具**")
    print("="*60)
    print("智能分析并去除数据库中的重复食材")
    print("="*60)

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 初始化去重器
        deduplicator = IngredientDeduplicator()

        # 分析数据库
        print("🚀 开始分析数据库...")
        analysis_result = deduplicator.analyze_database()

        if not analysis_result['duplicates']:
            print("\n🎉 恭喜！数据库中没有发现重复的食材。")
            return

        # 生成去重计划
        plan = deduplicator.generate_deduplication_plan(analysis_result)

        # 显示去重计划
        deduplicator.print_deduplication_plan(plan)

        # 询问是否继续
        print(f"\n⚠️ 即将删除 {plan['summary']['total_to_delete']} 个重复食材")
        print("这个操作不可逆，请确认您已经备份了数据库！")

        # 先进行试运行
        response = input("\n是否先进行试运行查看详细信息？(Y/n): ")
        if response.lower() != 'n':
            print("\n🔍 执行试运行...")
            dry_result = deduplicator.execute_deduplication(plan, dry_run=True)

            print(f"\n📋 试运行结果:")
            print(f"  🗑️ 将删除: {dry_result['deleted_count']} 个食材")
            print(f"  ✅ 将保留: {plan['summary']['total_to_keep']} 个食材")

            # 显示一些具体的删除示例
            if plan['to_delete']:
                print(f"\n📝 删除示例 (前5个):")
                for i, item in enumerate(plan['to_delete'][:5], 1):
                    created = item['created_at'] if item['created_at'] else '未知'
                    print(f"  {i}. {item['name']} (ID: {item['id']}, {item['category']}, {created})")
                if len(plan['to_delete']) > 5:
                    print(f"  ... 还有 {len(plan['to_delete']) - 5} 个")

        # 询问是否执行实际删除
        response = input(f"\n确认删除这 {plan['summary']['total_to_delete']} 个重复食材吗？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return

        # 最后确认
        response = input("⚠️ 最后确认：这个操作不可逆！输入 'DELETE' 确认删除: ")
        if response != 'DELETE':
            print("❌ 操作已取消")
            return

        # 执行实际删除
        print("\n🚀 执行实际删除操作...")
        execution_result = deduplicator.execute_deduplication(plan, dry_run=False)

        # 生成最终报告
        report = deduplicator.generate_report(analysis_result, plan, execution_result)
        deduplicator.print_final_report(report)

        # 显示最终统计
        if execution_result['deleted_count'] > 0:
            print(f"\n🎉 成功完成食材去重！")
            print(f"📊 数据库优化结果:")
            print(f"  📉 食材数量: {analysis_result['total_ingredients']} → {report['summary']['ingredients_remaining']}")
            print(f"  🗑️ 删除重复: {execution_result['deleted_count']} 个")
            print(f"  💾 节省空间: 减少了 {(execution_result['deleted_count']/analysis_result['total_ingredients']*100):.1f}% 的冗余数据")

if __name__ == "__main__":
    main()
