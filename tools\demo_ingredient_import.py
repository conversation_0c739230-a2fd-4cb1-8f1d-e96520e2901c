#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食材导入工具演示脚本
展示如何使用食材批量导入工具
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory, AdministrativeArea
    from tools.quick_ingredient_importer import QuickIngredientImporter
    from tools.analyze_foot_folder import analyze_foot_folder, print_analysis_report
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def demo_analyze_folder():
    """演示文件夹分析功能"""
    print("🔍 **演示：文件夹分析功能**")
    print("="*50)
    
    # 分析D:\FOOT文件夹（如果存在）
    foot_path = "D:\\FOOT"
    if os.path.exists(foot_path):
        print(f"📂 分析真实的FOOT文件夹: {foot_path}")
        analysis = analyze_foot_folder(foot_path)
        if analysis:
            print_analysis_report(analysis)
        else:
            print("❌ 分析失败")
    else:
        print(f"📂 FOOT文件夹不存在: {foot_path}")
        print("💡 您可以创建该文件夹并放入食材图片进行测试")

def demo_quick_import():
    """演示快速导入功能"""
    print("\n🚀 **演示：快速导入功能**")
    print("="*50)
    
    app = create_app()
    with app.app_context():
        # 创建快速导入器
        importer = QuickIngredientImporter(source_folder="demo_ingredients")
        
        # 设置目录
        if not importer.setup_directories():
            print("📁 演示目录已创建，请添加图片文件后重新运行")
            return
        
        print("📊 当前数据库中的食材数量:")
        ingredient_count = Ingredient.query.count()
        print(f"  总计: {ingredient_count} 个食材")
        
        # 按分类统计
        categories = db.session.query(
            IngredientCategory.name, 
            db.func.count(Ingredient.id)
        ).join(
            Ingredient, 
            IngredientCategory.id == Ingredient.category_id
        ).group_by(IngredientCategory.name).all()
        
        if categories:
            print("  分类分布:")
            for category_name, count in categories:
                print(f"    📂 {category_name}: {count} 个")
        
        print("\n💡 快速导入器功能:")
        print("  1. 支持从文件夹批量导入图片")
        print("  2. 自动压缩图片到400x400像素")
        print("  3. 智能去重检测")
        print("  4. 支持学校级数据隔离")
        print("  5. 可导入预定义测试数据")

def demo_batch_import_features():
    """演示批量导入器的功能特点"""
    print("\n📦 **演示：批量导入器功能特点**")
    print("="*50)
    
    print("🎯 **核心功能**:")
    print("  ✅ 智能文件夹扫描 - 自动识别图片和分类")
    print("  ✅ 高效图片压缩 - 压缩到400x400，节省85%+空间")
    print("  ✅ 智能去重检测 - 避免重复导入")
    print("  ✅ 自动分类推断 - 根据文件夹结构智能分类")
    print("  ✅ 批量数据处理 - 支持大量数据高效导入")
    print("  ✅ 学校级隔离 - 支持多租户数据隔离")
    print("  ✅ 详细进度跟踪 - 实时显示处理进度")
    print("  ✅ 完整错误处理 - 异常回滚，数据安全")
    
    print("\n📊 **性能特点**:")
    print("  🚀 批次处理 - 默认50个文件一批，可调整")
    print("  💾 内存优化 - 逐个处理图片，避免内存溢出")
    print("  🔄 事务安全 - 批次提交，出错自动回滚")
    print("  📈 进度显示 - 实时百分比进度")
    
    print("\n🛡️ **安全特性**:")
    print("  🔒 数据隔离 - 严格按学校权限控制")
    print("  🔍 重复检测 - 智能识别已存在食材")
    print("  📝 详细日志 - 完整的操作记录")
    print("  🔙 错误恢复 - 支持断点续传")

def demo_usage_scenarios():
    """演示使用场景"""
    print("\n🎭 **演示：使用场景**")
    print("="*50)
    
    scenarios = [
        {
            "title": "🏫 新学校初始化",
            "description": "新学校首次使用系统，需要导入大量基础食材数据",
            "steps": [
                "1. 准备食材图片，按分类整理到文件夹",
                "2. 运行批量导入器，指定学校区域ID",
                "3. 系统自动处理图片压缩和分类",
                "4. 生成详细导入报告"
            ]
        },
        {
            "title": "📦 供应商数据迁移",
            "description": "从供应商获得大量食材图片，需要批量导入",
            "steps": [
                "1. 将供应商提供的图片放入D:\\FOOT文件夹",
                "2. 先运行分析工具了解数据结构",
                "3. 运行批量导入器进行导入",
                "4. 检查导入报告，处理异常数据"
            ]
        },
        {
            "title": "🧪 测试环境搭建",
            "description": "快速搭建测试环境，导入测试数据",
            "steps": [
                "1. 运行快速导入器",
                "2. 选择导入预定义食材",
                "3. 系统自动创建测试数据",
                "4. 验证功能正常"
            ]
        },
        {
            "title": "🔄 数据更新维护",
            "description": "定期更新食材图片和信息",
            "steps": [
                "1. 将新的食材图片放入指定文件夹",
                "2. 运行导入工具，自动跳过重复",
                "3. 只导入新增的食材",
                "4. 保持数据最新状态"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{scenario['title']}")
        print(f"📝 {scenario['description']}")
        print("🔧 操作步骤:")
        for step in scenario['steps']:
            print(f"   {step}")

def demo_best_practices():
    """演示最佳实践"""
    print("\n💡 **演示：最佳实践建议**")
    print("="*50)
    
    practices = [
        {
            "category": "📁 文件组织",
            "tips": [
                "按食材分类创建文件夹（蔬菜、肉类、水果等）",
                "文件名包含食材名称，便于自动识别",
                "使用标准图片格式（JPG、PNG）",
                "图片尺寸建议800x600以上，系统会自动压缩"
            ]
        },
        {
            "category": "🔄 导入流程",
            "tips": [
                "导入前先运行分析工具了解数据结构",
                "小批量测试后再进行大批量导入",
                "定期备份数据库，防止意外情况",
                "检查导入报告，及时处理错误"
            ]
        },
        {
            "category": "🏫 学校管理",
            "tips": [
                "为每个学校指定独立的区域ID",
                "全局食材设置为系统级别，供所有学校使用",
                "学校特有食材设置为学校级别",
                "定期清理无用的食材数据"
            ]
        },
        {
            "category": "⚡ 性能优化",
            "tips": [
                "大量数据导入时调整批次大小",
                "在网络存储上的文件先复制到本地",
                "避免在业务高峰期进行大批量导入",
                "监控系统资源使用情况"
            ]
        }
    ]
    
    for practice in practices:
        print(f"\n{practice['category']}")
        for tip in practice['tips']:
            print(f"  ✅ {tip}")

def main():
    """主演示函数"""
    print("🍎 **食材批量导入工具演示**")
    print("="*60)
    print("本演示将展示食材导入工具的各项功能和使用方法")
    
    # 演示各个功能
    demo_analyze_folder()
    demo_quick_import()
    demo_batch_import_features()
    demo_usage_scenarios()
    demo_best_practices()
    
    print("\n" + "="*60)
    print("🎉 **演示完成！**")
    print("\n📚 **下一步操作建议**:")
    print("1. 📖 阅读 tools/README_食材批量导入工具.md 详细文档")
    print("2. 🧪 运行 python tools/test_ingredient_importer.py 验证功能")
    print("3. 🚀 运行 python tools/quick_ingredient_importer.py 快速导入")
    print("4. 📦 运行 python tools/ingredient_batch_importer.py 批量导入")
    print("\n💡 **技术支持**:")
    print("如有问题，请查看工具生成的错误日志和导入报告")

if __name__ == "__main__":
    main()
