#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试蔬菜智能去重功能
验证文件名标准化是否正确工作
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from tools.vegetable_smart_importer import VegetableSmartImporter

def test_normalize_function():
    """测试文件名标准化功能"""
    print("🧪 **测试文件名标准化功能**")
    print("="*50)
    
    importer = VegetableSmartImporter()
    
    # 测试用例
    test_cases = [
        # (原始文件名, 期望结果)
        ("西红柿1", "西红柿"),
        ("西红柿2", "西红柿"),
        ("西红柿_3", "西红柿"),
        ("西红柿-4", "西红柿"),
        ("西红柿 5", "西红柿"),
        ("土豆(1)", "土豆"),
        ("土豆(2)", "土豆"),
        ("胡萝卜[1]", "胡萝卜"),
        ("胡萝卜[2]", "胡萝卜"),
        ("白菜_123", "白菜"),
        ("茄子-456", "茄子"),
        ("黄瓜 789", "黄瓜"),
        ("青椒", "青椒"),  # 没有数字后缀
        ("洋葱abc", "洋葱abc"),  # 非数字后缀，保持不变
        ("", ""),  # 空字符串
    ]
    
    print("📋 测试用例:")
    all_passed = True
    
    for i, (original, expected) in enumerate(test_cases, 1):
        result = importer.normalize_vegetable_name(original)
        status = "✅" if result == expected else "❌"
        
        print(f"  {i:2d}. '{original}' → '{result}' (期望: '{expected}') {status}")
        
        if result != expected:
            all_passed = False
    
    print(f"\n📊 测试结果: {'全部通过' if all_passed else '有失败用例'}")
    return all_passed

def test_scan_footsc_folder():
    """测试扫描D:\FOOTsc文件夹"""
    print("\n🔍 **测试扫描D:\\FOOTsc文件夹**")
    print("="*50)
    
    source_folder = Path("D:/FOOTsc")
    if not source_folder.exists():
        print("❌ D:\\FOOTsc文件夹不存在，跳过测试")
        return False
    
    importer = VegetableSmartImporter()
    
    # 只进行扫描，不实际导入
    scan_result = importer.scan_and_deduplicate()
    
    if not scan_result:
        print("❌ 扫描失败")
        return False
    
    print(f"📊 扫描结果:")
    print(f"  📄 总文件数: {scan_result.get('total_images', 0)}")
    print(f"  🥬 唯一蔬菜: {scan_result.get('unique_vegetables', 0)}")
    print(f"  🔄 重复文件: {scan_result.get('duplicate_count', 0)}")
    
    # 显示去重效果
    if scan_result.get('duplicate_count', 0) > 0:
        reduction_rate = (scan_result['duplicate_count'] / scan_result['total_images']) * 100
        print(f"  📈 去重效果: 减少 {reduction_rate:.1f}% 的重复文件")
    
    # 显示一些具体的去重示例
    vegetable_groups = scan_result.get('vegetable_groups', {})
    name_mapping = scan_result.get('name_mapping', {})
    
    duplicates = {name: files for name, files in vegetable_groups.items() if len(files) > 1}
    if duplicates:
        print(f"\n📋 去重示例 (前5个):")
        for i, (normalized_name, files) in enumerate(list(duplicates.items())[:5], 1):
            original_names = name_mapping.get(normalized_name, [])
            print(f"  {i}. {normalized_name} ({len(files)} 个文件)")
            print(f"     原始: {', '.join(original_names[:3])}")
            if len(original_names) > 3:
                print(f"     ... 还有 {len(original_names) - 3} 个")
    
    return True

def analyze_footsc_structure():
    """分析D:\FOOTsc文件夹结构"""
    print("\n📂 **分析D:\\FOOTsc文件夹结构**")
    print("="*50)
    
    source_folder = Path("D:/FOOTsc")
    if not source_folder.exists():
        print("❌ D:\\FOOTsc文件夹不存在")
        return
    
    # 获取所有图片文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(source_folder.rglob(f"*{ext}"))
        image_files.extend(source_folder.rglob(f"*{ext.upper()}"))
    
    if not image_files:
        print("❌ 没有找到图片文件")
        return
    
    print(f"📊 文件夹分析:")
    print(f"  📄 图片文件总数: {len(image_files)}")
    
    # 分析文件名模式
    names = [f.stem for f in image_files]
    
    # 统计有数字后缀的文件
    importer = VegetableSmartImporter()
    normalized_names = [importer.normalize_vegetable_name(name) for name in names]
    
    # 计算去重效果
    unique_original = len(set(names))
    unique_normalized = len(set(normalized_names))
    
    print(f"  📝 原始唯一名称: {unique_original}")
    print(f"  🎯 标准化后唯一名称: {unique_normalized}")
    print(f"  📈 去重效果: 减少 {unique_original - unique_normalized} 个重复")
    
    # 显示一些文件名示例
    print(f"\n📋 文件名示例 (前20个):")
    for i, name in enumerate(names[:20], 1):
        normalized = importer.normalize_vegetable_name(name)
        if name != normalized:
            print(f"  {i:2d}. {name} → {normalized}")
        else:
            print(f"  {i:2d}. {name}")

def main():
    """主函数"""
    print("🥬 **蔬菜智能去重功能测试**")
    print("="*60)
    
    # 测试标准化功能
    normalize_passed = test_normalize_function()
    
    # 分析文件夹结构
    analyze_footsc_structure()
    
    # 测试扫描功能
    scan_passed = test_scan_footsc_folder()
    
    print(f"\n📊 **总体测试结果**")
    print("="*60)
    print(f"✅ 标准化功能: {'通过' if normalize_passed else '失败'}")
    print(f"✅ 扫描功能: {'通过' if scan_passed else '失败'}")
    
    if normalize_passed and scan_passed:
        print(f"\n🎉 所有测试通过！可以运行完整的蔬菜导入工具:")
        print(f"   python tools/vegetable_smart_importer.py")
    else:
        print(f"\n❌ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
