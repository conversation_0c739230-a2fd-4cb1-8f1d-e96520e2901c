{"summary": {"total_images_scanned": 868, "unique_recipes_found": 178, "recipes_created": 3, "recipes_updated": 0, "ingredients_added": 0, "errors_occurred": 175, "duration_seconds": 13.403851, "success_rate": 1.6853932584269662}, "processed_recipes": [{"id": 182, "name": "青菜钵", "category": "素菜", "ingredient_count": 6, "image_file": "20250530223452_26cef8c1.jpg"}, {"id": 183, "name": "一品烩山药", "category": "素菜", "ingredient_count": 6, "image_file": "20250530223452_5c41a355.jpg"}, {"id": 184, "name": "一品茄子", "category": "素菜", "ingredient_count": 6, "image_file": "20250530223452_09fb1959.jpg"}], "errors": ["创建食谱失败 一品豆腐烧草菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 三鲜杏鲍菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 三鲜菠菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 上汤菠菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 丝瓜烩鲜鲍: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 乡村土豆泥: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 乡村老南瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 乡村萝卜丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 八宝南瓜元份: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 养生地木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 养生菠菜丸: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 养身山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 养身青菜钵: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 农家炒山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 农家炒淮山: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 冰淇淋土豆泥: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 凉拌金针菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 刘爹土豆丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 刨花青笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 剁椒土豆丝元: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 剁椒笋片: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 南瓜饼: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 双椒茄子: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 口味豆角结: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 吊烧茄子: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 回味椒盐小笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 土豆丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 土豆烧裙边: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 地软菜干捞粉丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 大碗冬瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 大碗长豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 大蒜辣椒炒油渣: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 大豆芽炒粉条: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小厨白菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小土豆烩四季豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小土豆焖兔: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小土豆焖大雁: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒口蘑: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒土豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒杏鲍菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒沃白菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒黄笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小炒黑木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小白菜炖豆腐: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小白菜芋儿: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小米椒炒木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小米椒爆鲜鱿: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小米青菜钵: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小葱拌辽参: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 小青菜煎豆腐: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 干锅茶树菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 干锅萝卜丝煮小笼包: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 手撕包包菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 手撕包菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 拍蒜彩椒鳝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 木耳炒藕片: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 木耳青笋片: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 杏仁萝卜苗: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 果王烤蘑菇炒菠菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 枣香秘汁山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 椒香青豌豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 橄榄菜四季豆+: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 橄榄菜杂菌炒豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 水煮干豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 油鬼丝瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 泡椒海笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 泰椒炒黑木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 泰皇汁茄子: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 洞庭蚕豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 浆水炒豆芽: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 浆水菜炒豆芽+: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 海味丝瓜烧油条元例: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 海味丝瓜烧油条: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 海香菇元份: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 清炒山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘养颜冬瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘砂锅青椒鳝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘豆豉辣椒烤油渣: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘顺风卜豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘式小土豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘菜手撕包菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘菜油姜圣子王: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘菜青椒卜豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘菜香菇焖白鹜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 湘菜黄焖烟笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 炝拌苦菊: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 炝拌黄花菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 烧汁茶树菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 甜香槐花: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 生炝菠菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 白辣椒蒸甘泉豆腐: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂窝四季豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂窝土豆片: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂窝大豆芽: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂窝杏鲍菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂窝蒜苔: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂窝西葫芦: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂锅花菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 砂锅莴笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 筒笋相思尾: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 米椒木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 米油萝卜丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 粉条炒酸包菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 红果山药元例: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 红烧小土豆元: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 红烧茄子元.: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 纸锅干鱿笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 美味土豆粉: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 美极木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 美极杏鲍菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 美极萝卜元: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 老厨白菜元: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 老干妈土豆丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 艳阳土豆丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 芹菜羹: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 苏氏小土豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 英姿萝卜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 茄子烧豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 茶菇焗豆角: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 莲菜炒韭菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 葱爆海带茎: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 蒜片黄瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 蒜香掌中宝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 藕丁掌中宝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 蜈蚣上汤笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 蜜豆百合炒酿木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 西芹山药炒百合: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 豆汤青菜钵元份咸鲜味: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 豆角茄子: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 豇豆炒面: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 豇豆角烧茄子: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酱爆刀豆: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸包菜炒粉丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸菜土豆丝炒粉条: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸菜炒小笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸菜炒竹笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸萝卜炒辽参丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸豇豆炒雀胗: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸辣土豆丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 酸辣白菜炒蜇头: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 醋溜黑木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 金沙莲藕夹: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 钵仔菜花: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 钵子花菜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 铁板杏鲍菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 铁板韭菜香干: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 银杏酿南瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 长豇豆烧茄子: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 青椒拌豆腐丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 青椒炒仔排: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 青瓜小海鲜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 青瓜木耳炒鲜鱿: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 青菜土豆泥: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 青菜拌蛰头: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 韭菜桃仁: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 韭菜炒藕丝: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 韭菜炒豆芽: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 韭菜炒软饼元: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 韭菜花炒鳝丝元例: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 香芹木耳炒淮山: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 香芹萝卜干炒烟笋: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 香菇焖西兰花: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 香辣炒野生木耳: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲍汁海皇南瓜盅: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲍汁茶树菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲜桃仁木耳炒山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲜椒仔姜蛙: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲜椒白灵菇: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲜椒青瓜干: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲜笋烧仔排: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 鲜蚕豆烧大雁: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 麦香南瓜: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 黄米南瓜盅: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "创建食谱失败 黑木耳炒山药: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)", "数据库提交失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')\n[SQL: INSERT INTO ingredients (name, category, category_id, area_id, unit, standard_unit, base_image, storage_temp, storage_condition, shelf_life, specification, nutrition_info, is_condiment, is_global, status, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('草菇', '其他蔬菜', 19, None, 'g', None, None, None, None, None, None, None, 0, 1, 1, datetime.datetime(2025, 5, 30, 22, 34, 53), datetime.datetime(2025, 5, 30, 22, 34, 53))]\n(Background on this error at: https://sqlalche.me/e/20/dbapi) (Background on this error at: https://sqlalche.me/e/20/7s2a)"], "timestamp": "2025-05-30T22:34:53.139962"}