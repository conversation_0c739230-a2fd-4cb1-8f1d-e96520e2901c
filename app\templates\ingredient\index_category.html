{% extends 'base.html' %}

{% block title %}食材管理 - 按分类显示{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">食材管理 - 按分类显示</h3>
                        <div class="card-tools">
                            <!-- 显示模式切换 -->
                            <div class="btn-group mr-2" role="group">
                                <a href="{{ url_for('ingredient.index', view_mode='list', category_id=category_id, keyword=keyword) }}" 
                                   class="btn btn-sm {{ 'btn-primary' if view_mode == 'list' else 'btn-outline-primary' }}">
                                    <i class="fas fa-list"></i> 列表
                                </a>
                                <a href="{{ url_for('ingredient.index', view_mode='category', category_id=category_id, keyword=keyword) }}" 
                                   class="btn btn-sm {{ 'btn-primary' if view_mode == 'category' else 'btn-outline-primary' }}">
                                    <i class="fas fa-th-large"></i> 分类
                                </a>
                            </div>
                            <a href="{{ url_for('ingredient.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加食材
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <input type="hidden" name="view_mode" value="{{ view_mode }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="category_id">食材分类</label>
                                    <select class="form-control" id="category_id" name="category_id">
                                        <option value="">-- 所有分类 --</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" value="{{ keyword }}" placeholder="食材名称">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group" style="margin-top: 32px;">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <a href="{{ url_for('ingredient.index', view_mode='category') }}" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 按分类显示食材 -->
                    {% if ingredients_by_category %}
                        {% for category_name, ingredients in ingredients_by_category.items() %}
                        <div class="category-section mb-4">
                            <div class="category-header">
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-tag"></i> {{ category_name }}
                                    <span class="badge badge-secondary ml-2">{{ ingredients|length }}</span>
                                </h4>
                            </div>
                            
                            <div class="row">
                                {% for ingredient in ingredients %}
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                    <div class="card ingredient-card h-100">
                                        <div class="card-body p-3">
                                            <!-- 食材图片 -->
                                            <div class="text-center mb-2">
                                                {% if ingredient.base_image %}
                                                <img src="{{ url_for('static', filename=ingredient.base_image) }}"
                                                     alt="{{ ingredient.name }}"
                                                     class="img-fluid rounded"
                                                     style="max-height: 80px; max-width: 100%;"
                                                     onerror="this.src='{{ url_for('static', filename='img/qr-code-placeholder.png') }}'; this.onerror=null;">
                                                {% else %}
                                                <img src="{{ url_for('static', filename='img/qr-code-placeholder.png') }}" 
                                                     alt="No Image" 
                                                     class="img-fluid rounded" 
                                                     style="max-height: 80px; max-width: 100%;">
                                                {% endif %}
                                            </div>
                                            
                                            <!-- 食材信息 -->
                                            <h6 class="card-title text-center mb-2">{{ ingredient.name }}</h6>
                                            
                                            <div class="ingredient-info">
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-cube"></i> 规格: {{ ingredient.specification or '-' }}
                                                </small>
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-balance-scale"></i> 单位: {{ ingredient.unit }}
                                                </small>
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-thermometer-half"></i> 存储: {{ ingredient.storage_condition or ingredient.storage_temp or '-' }}
                                                </small>
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-calendar-alt"></i> 保质期: {{ ingredient.shelf_life or '-' }}天
                                                </small>
                                            </div>
                                            
                                            <!-- 状态标识 -->
                                            <div class="text-center mt-2">
                                                {% if ingredient.status == 1 %}
                                                <span class="badge badge-success">启用</span>
                                                {% else %}
                                                <span class="badge badge-danger">停用</span>
                                                {% endif %}
                                                
                                                {% if ingredient.is_global %}
                                                <span class="badge badge-info">系统</span>
                                                {% else %}
                                                <span class="badge badge-warning">学校</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <!-- 操作按钮 -->
                                        <div class="card-footer p-2">
                                            <div class="btn-group btn-group-sm w-100" role="group">
                                                <a href="{{ url_for('ingredient.view', id=ingredient.id) }}" 
                                                   class="btn btn-info" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('ingredient.edit', id=ingredient.id) }}" 
                                                   class="btn btn-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('ingredient.turnover', id=ingredient.id) }}" 
                                                   class="btn btn-success" title="周转情况">
                                                    <i class="fas fa-chart-line"></i>
                                                </a>
                                                <button class="btn btn-danger delete-btn" 
                                                        data-id="{{ ingredient.id }}" 
                                                        title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">没有找到符合条件的食材</h5>
                            <p class="text-muted">请尝试调整搜索条件或添加新的食材</p>
                            <a href="{{ url_for('ingredient.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加食材
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个食材吗？此操作不可逆。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
.ingredient-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid #e3e6f0;
}

.ingredient-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.category-header {
    border-bottom: 2px solid #e3e6f0;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.ingredient-info {
    font-size: 12px;
    line-height: 1.4;
}

.ingredient-info small {
    margin-bottom: 2px;
}

.card-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

.btn-group-sm .btn {
    font-size: 11px;
    padding: 0.25rem 0.4rem;
}
</style>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("ingredient.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
